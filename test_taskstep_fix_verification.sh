#!/bin/bash

# =============================================================================
# TaskStep修复验证脚本
# 验证修复后的TaskStep状态是否正确更新
# =============================================================================

echo "=== TaskStep修复验证测试开始 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 创建测试任务
echo -e "${BLUE}1. 创建测试任务（修复后）...${NC}"
task_response=$(curl -s -X POST http://localhost:8080/api/compliance-agent/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": 1,
    "taskType": "REGULATION_INTERNALIZATION",
    "title": "TaskStep修复验证测试",
    "description": "验证TaskStep状态是否正确更新",
    "priority": "HIGH",
    "requestData": "{\"regulationId\":\"REG_FIX_TEST_001\",\"companyId\":1,\"industryType\":\"电力\",\"tenantId\":1,\"employeeId\":1001}"
  }')

task_id=$(echo "$task_response" | jq -r '.taskId')

if [ "$task_id" != "null" ] && [ "$task_id" != "" ]; then
    echo -e "${GREEN}✓ 任务创建成功，任务ID: $task_id${NC}"
else
    echo -e "${RED}✗ 任务创建失败${NC}"
    echo "响应: $task_response"
    exit 1
fi

# 2. 等待任务执行
echo -e "${YELLOW}2. 等待任务执行（20秒）...${NC}"
sleep 20

# 3. 检查AgentTask状态
echo -e "${BLUE}3. 检查AgentTask状态...${NC}"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    status,
    progress,
    start_time,
    end_time,
    CASE 
        WHEN execution_time IS NOT NULL THEN CONCAT(execution_time, 'ms')
        ELSE 'NULL'
    END as execution_time
FROM agent_task 
WHERE id = $task_id;
"

# 4. 检查TaskStep状态（这是关键！）
echo -e "${BLUE}4. 检查TaskStep状态更新（关键验证）...${NC}"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    step_name,
    step_order,
    status,
    start_time,
    end_time,
    CASE 
        WHEN execution_time IS NOT NULL THEN CONCAT(execution_time, 'ms')
        ELSE 'NULL'
    END as execution_time,
    step_type
FROM task_step 
WHERE agent_task_id = $task_id 
ORDER BY step_order;
"

# 5. 检查AgentContext状态
echo -e "${BLUE}5. 检查AgentContext状态...${NC}"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    agent_task_id,
    context_type,
    created_at
FROM agent_context 
WHERE agent_task_id = $task_id;
"

# 6. 统计分析
echo -e "${BLUE}6. 修复效果统计分析...${NC}"

# 统计TaskStep状态分布
echo "TaskStep状态分布："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id), 2) as percentage
FROM task_step 
WHERE agent_task_id = $task_id 
GROUP BY status;
"

# 检查关键字段是否有数据
echo ""
echo "关键字段数据检查："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    '有开始时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND start_time IS NOT NULL
UNION ALL
SELECT 
    '有结束时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND end_time IS NOT NULL
UNION ALL
SELECT 
    '有执行时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND execution_time IS NOT NULL
UNION ALL
SELECT 
    '有步骤类型的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND step_type IS NOT NULL
UNION ALL
SELECT 
    '状态为COMPLETED的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND status = 'COMPLETED'
UNION ALL
SELECT 
    '状态为PENDING的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND status = 'PENDING';
"

# 7. 修复效果评估
echo -e "${BLUE}7. 修复效果评估...${NC}"

# 检查是否还有PENDING状态的步骤
pending_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND status = 'PENDING';
" | tail -n 1)

# 检查是否有COMPLETED状态的步骤
completed_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND status = 'COMPLETED';
" | tail -n 1)

# 检查是否有时间戳数据
timestamp_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND start_time IS NOT NULL;
" | tail -n 1)

# 检查是否有步骤类型数据
steptype_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND step_type IS NOT NULL;
" | tail -n 1)

echo ""
echo -e "${YELLOW}修复效果评估结果：${NC}"

if [ "$pending_count" -eq 0 ]; then
    echo -e "${GREEN}✓ 所有步骤状态已更新（无PENDING状态）${NC}"
else
    echo -e "${RED}✗ 仍有 $pending_count 个步骤处于PENDING状态${NC}"
fi

if [ "$completed_count" -gt 0 ]; then
    echo -e "${GREEN}✓ 有 $completed_count 个步骤状态为COMPLETED${NC}"
else
    echo -e "${RED}✗ 没有步骤状态为COMPLETED${NC}"
fi

if [ "$timestamp_count" -gt 0 ]; then
    echo -e "${GREEN}✓ TaskStep时间戳数据已记录（$timestamp_count 个步骤有开始时间）${NC}"
else
    echo -e "${RED}✗ TaskStep时间戳数据仍为空${NC}"
fi

if [ "$steptype_count" -gt 0 ]; then
    echo -e "${GREEN}✓ TaskStep类型数据已记录（$steptype_count 个步骤有类型）${NC}"
else
    echo -e "${RED}✗ TaskStep类型数据仍为空${NC}"
fi

# 8. 总结
echo ""
echo -e "${GREEN}=== TaskStep修复验证测试完成 ===${NC}"
echo ""
echo -e "${BLUE}测试任务ID: $task_id${NC}"

# 计算修复成功率
total_checks=4
success_checks=0

[ "$pending_count" -eq 0 ] && ((success_checks++))
[ "$completed_count" -gt 0 ] && ((success_checks++))
[ "$timestamp_count" -gt 0 ] && ((success_checks++))
[ "$steptype_count" -gt 0 ] && ((success_checks++))

success_rate=$((success_checks * 100 / total_checks))

echo -e "${BLUE}修复成功率: ${success_rate}%${NC}"

if [ "$success_rate" -ge 75 ]; then
    echo -e "${GREEN}🎉 修复效果良好！${NC}"
elif [ "$success_rate" -ge 50 ]; then
    echo -e "${YELLOW}⚠️  修复部分成功，需要进一步调试${NC}"
else
    echo -e "${RED}❌ 修复效果不佳，需要重新检查代码${NC}"
fi

echo ""
echo -e "${BLUE}可以通过以下SQL进一步检查：${NC}"
echo "SELECT * FROM task_step WHERE agent_task_id = $task_id ORDER BY step_order;"
echo "SELECT * FROM agent_task WHERE id = $task_id;"
echo "SELECT * FROM agent_context WHERE agent_task_id = $task_id;"
