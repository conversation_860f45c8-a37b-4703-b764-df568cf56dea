#!/bin/bash

# =============================================================================
# 并行执行验证脚本
# 验证ComplianceAgentController的并行执行能力
# =============================================================================

echo "=== Agent模块并行执行验证开始 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查应用是否运行
check_app_status() {
    echo -e "${BLUE}1. 检查应用状态...${NC}"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/management/health)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✓ 应用正在运行${NC}"
        return 0
    else
        echo -e "${RED}✗ 应用未运行，请先启动应用${NC}"
        return 1
    fi
}

# 测试单个任务的并行执行
test_single_task_parallel() {
    echo -e "${BLUE}2. 测试单个任务内部步骤并行执行...${NC}"
    
    start_time=$(date +%s%3N)
    
    # 创建合同审查任务
    task_response=$(curl -s -X POST http://localhost:8080/api/compliance-agent/tasks \
      -H "Content-Type: application/json" \
      -d '{
        "tenantId": 1,
        "taskType": "CONTRACT_REVIEW",
        "title": "并行执行验证测试",
        "description": "验证合同审查任务的并行执行能力",
        "priority": "HIGH",
        "requestData": "{\"contractId\":\"PARALLEL_TEST_001\",\"contractType\":\"采购合同\",\"tenantId\":1,\"employeeId\":1001}"
      }')
    
    task_id=$(echo "$task_response" | jq -r '.taskId')
    
    if [ "$task_id" != "null" ] && [ "$task_id" != "" ]; then
        echo -e "${GREEN}✓ 任务创建成功，任务ID: $task_id${NC}"
        
        # 等待任务执行
        echo -e "${YELLOW}等待任务执行（10秒）...${NC}"
        sleep 10
        
        end_time=$(date +%s%3N)
        execution_time=$((end_time - start_time))
        
        echo -e "${GREEN}✓ 单任务执行完成，耗时: ${execution_time}ms${NC}"
        
        # 检查数据库中的TaskStep记录
        echo -e "${BLUE}检查TaskStep并行执行记录...${NC}"
        echo "可以通过以下SQL查看步骤执行情况："
        echo "SELECT step_name, status, start_time, end_time FROM task_step WHERE agent_task_id = $task_id ORDER BY step_order;"
        
    else
        echo -e "${RED}✗ 任务创建失败${NC}"
        return 1
    fi
}

# 测试多任务并发执行
test_multiple_tasks_concurrent() {
    echo -e "${BLUE}3. 测试多任务并发执行...${NC}"
    
    start_time=$(date +%s%3N)
    task_ids=()
    
    # 并发创建5个不同类型的任务
    for i in {1..5}; do
        case $((i % 3)) in
            1)
                task_type="CONTRACT_REVIEW"
                request_data="{\"contractId\":\"CONCURRENT_$i\",\"contractType\":\"采购合同\",\"tenantId\":1,\"employeeId\":1001}"
                ;;
            2)
                task_type="POLICY_REVIEW"
                request_data="{\"policyId\":\"POLICY_$i\",\"policyType\":\"安全管理\",\"tenantId\":1,\"employeeId\":1001}"
                ;;
            0)
                task_type="REGULATION_INTERNALIZATION"
                request_data="{\"regulationId\":\"REG_$i\",\"companyId\":1,\"industryType\":\"电力\",\"tenantId\":1,\"employeeId\":1001}"
                ;;
        esac
        
        # 后台并发执行
        (
            response=$(curl -s -X POST http://localhost:8080/api/compliance-agent/tasks \
              -H "Content-Type: application/json" \
              -d "{
                \"tenantId\": 1,
                \"taskType\": \"$task_type\",
                \"title\": \"并发测试任务 #$i\",
                \"description\": \"测试任务$i的并发执行\",
                \"priority\": \"NORMAL\",
                \"requestData\": \"$request_data\"
              }")
            
            task_id=$(echo "$response" | jq -r '.taskId')
            echo -e "${GREEN}✓ 并发任务 #$i 创建成功，ID: $task_id, 类型: $task_type${NC}"
        ) &
    done
    
    # 等待所有后台任务完成
    wait
    
    end_time=$(date +%s%3N)
    execution_time=$((end_time - start_time))
    
    echo -e "${GREEN}✓ 5个并发任务创建完成，总耗时: ${execution_time}ms${NC}"
    echo -e "${GREEN}✓ 平均每任务创建时间: $((execution_time / 5))ms${NC}"
}

# 验证并行执行效果
verify_parallel_execution_effects() {
    echo -e "${BLUE}4. 验证并行执行效果...${NC}"
    
    echo -e "${YELLOW}检查应用日志中的并行执行信息：${NC}"
    echo "1. 查找 '开始智能并行执行工作流' 日志"
    echo "2. 查找 '工作流分析完成' 日志"
    echo "3. 查找 '执行第X组并行步骤' 日志"
    echo "4. 查找 '并行步骤执行完成' 日志"
    
    echo ""
    echo -e "${YELLOW}预期的并行执行模式：${NC}"
    echo "第1组：检索类步骤（法规检索、行业实践检索、企业制度检索）- 并行执行"
    echo "第2组：分析类步骤（内容分析、风险分析）- 并行执行"
    echo "第3组：生成类步骤（制度生成、报告生成）- 并行执行"
    echo "第4组：其他步骤（验证、整合）- 顺序执行"
}

# 性能对比分析
performance_analysis() {
    echo -e "${BLUE}5. 性能对比分析...${NC}"
    
    echo -e "${YELLOW}理论性能提升：${NC}"
    echo "• 检索步骤并行化：节省 60-70% 时间"
    echo "• 分析步骤并行化：节省 40-50% 时间"
    echo "• 整体任务执行：预期提升 30-50%"
    
    echo ""
    echo -e "${YELLOW}实际验证方法：${NC}"
    echo "1. 对比TaskStep的start_time和end_time"
    echo "2. 检查同组步骤的时间重叠情况"
    echo "3. 计算总执行时间与串行执行的差异"
}

# 主执行流程
main() {
    echo "开始时间: $(date)"
    echo ""
    
    # 检查应用状态
    if ! check_app_status; then
        exit 1
    fi
    
    echo ""
    
    # 测试单任务并行执行
    test_single_task_parallel
    
    echo ""
    
    # 测试多任务并发执行
    test_multiple_tasks_concurrent
    
    echo ""
    
    # 验证并行执行效果
    verify_parallel_execution_effects
    
    echo ""
    
    # 性能分析
    performance_analysis
    
    echo ""
    echo -e "${GREEN}=== Agent模块并行执行验证完成 ===${NC}"
    echo "结束时间: $(date)"
    
    echo ""
    echo -e "${BLUE}后续验证建议：${NC}"
    echo "1. 查看应用日志确认并行执行日志"
    echo "2. 检查数据库TaskStep表的执行时间"
    echo "3. 监控系统资源使用情况"
    echo "4. 在生产环境中进行真实性能测试"
}

# 执行主流程
main "$@"
