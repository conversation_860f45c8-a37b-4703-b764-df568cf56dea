#!/bin/bash

# =============================================================================
# TaskStep状态更新测试脚本
# 验证修复后的TaskStep状态是否正确更新
# =============================================================================

echo "=== TaskStep状态更新测试开始 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 创建测试任务
echo -e "${BLUE}1. 创建测试任务...${NC}"
task_response=$(curl -s -X POST http://localhost:8080/api/compliance-agent/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": 1,
    "taskType": "CONTRACT_REVIEW",
    "title": "TaskStep状态更新测试",
    "description": "测试TaskStep状态是否正确更新",
    "priority": "HIGH",
    "requestData": "{\"contractId\":\"STATUS_TEST_001\",\"contractType\":\"采购合同\",\"tenantId\":1,\"employeeId\":1001}"
  }')

task_id=$(echo "$task_response" | jq -r '.taskId')

if [ "$task_id" != "null" ] && [ "$task_id" != "" ]; then
    echo -e "${GREEN}✓ 任务创建成功，任务ID: $task_id${NC}"
else
    echo -e "${RED}✗ 任务创建失败${NC}"
    exit 1
fi

# 2. 等待任务执行
echo -e "${YELLOW}2. 等待任务执行（15秒）...${NC}"
sleep 15

# 3. 检查TaskStep状态
echo -e "${BLUE}3. 检查TaskStep状态更新...${NC}"

# 查询TaskStep数据
echo "查询TaskStep状态："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    step_name,
    step_order,
    status,
    start_time,
    end_time,
    CASE 
        WHEN execution_time IS NOT NULL THEN CONCAT(execution_time, 'ms')
        ELSE 'NULL'
    END as execution_time,
    step_type
FROM task_step 
WHERE agent_task_id = $task_id 
ORDER BY step_order;
"

# 4. 检查AgentTask状态
echo -e "${BLUE}4. 检查AgentTask状态...${NC}"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    status,
    progress,
    start_time,
    end_time,
    CASE 
        WHEN execution_time IS NOT NULL THEN CONCAT(execution_time, 'ms')
        ELSE 'NULL'
    END as execution_time
FROM agent_task 
WHERE id = $task_id;
"

# 5. 统计分析
echo -e "${BLUE}5. 统计分析...${NC}"

# 统计TaskStep状态分布
echo "TaskStep状态分布："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id), 2) as percentage
FROM task_step 
WHERE agent_task_id = $task_id 
GROUP BY status;
"

# 检查是否有时间戳数据
echo "时间戳数据检查："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    '有开始时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND start_time IS NOT NULL
UNION ALL
SELECT 
    '有结束时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND end_time IS NOT NULL
UNION ALL
SELECT 
    '有执行时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND execution_time IS NOT NULL
UNION ALL
SELECT 
    '有步骤类型的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND step_type IS NOT NULL;
"

# 6. 验证结果
echo -e "${BLUE}6. 验证修复效果...${NC}"

# 检查是否还有PENDING状态的步骤
pending_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND status = 'PENDING';
" | tail -n 1)

# 检查是否有时间戳数据
timestamp_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND start_time IS NOT NULL;
" | tail -n 1)

echo ""
echo -e "${YELLOW}修复效果评估：${NC}"

if [ "$pending_count" -eq 0 ]; then
    echo -e "${GREEN}✓ 所有步骤状态已更新（无PENDING状态）${NC}"
else
    echo -e "${RED}✗ 仍有 $pending_count 个步骤处于PENDING状态${NC}"
fi

if [ "$timestamp_count" -gt 0 ]; then
    echo -e "${GREEN}✓ TaskStep时间戳数据已记录（$timestamp_count 个步骤有开始时间）${NC}"
else
    echo -e "${RED}✗ TaskStep时间戳数据仍为空${NC}"
fi

echo ""
echo -e "${GREEN}=== TaskStep状态更新测试完成 ===${NC}"
echo ""
echo -e "${BLUE}测试任务ID: $task_id${NC}"
echo -e "${BLUE}可以通过以下SQL进一步检查：${NC}"
echo "SELECT * FROM task_step WHERE agent_task_id = $task_id ORDER BY step_order;"
echo "SELECT * FROM agent_task WHERE id = $task_id;"
echo "SELECT * FROM agent_context WHERE agent_task_id = $task_id;"
