package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AgentContextAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.repository.AgentContextRepository;
import com.whiskerguard.ai.service.AgentContextService;
import com.whiskerguard.ai.service.dto.AgentContextDTO;
import com.whiskerguard.ai.service.mapper.AgentContextMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AgentContextResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class AgentContextResourceIT {

    private static final Long DEFAULT_AGENT_TASK_ID = 1L;
    private static final Long UPDATED_AGENT_TASK_ID = 2L;

    private static final String DEFAULT_CONTEXT_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_CONTEXT_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_CONTEXT_DATA = "AAAAAAAAAA";
    private static final String UPDATED_CONTEXT_DATA = "BBBBBBBBBB";

    private static final String DEFAULT_VARIABLES = "AAAAAAAAAA";
    private static final String UPDATED_VARIABLES = "BBBBBBBBBB";

    private static final String DEFAULT_TEMP_FILES = "AAAAAAAAAA";
    private static final String UPDATED_TEMP_FILES = "BBBBBBBBBB";

    private static final String DEFAULT_HISTORY = "AAAAAAAAAA";
    private static final String UPDATED_HISTORY = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/agent-contexts";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AgentContextRepository agentContextRepository;

    @Mock
    private AgentContextRepository agentContextRepositoryMock;

    @Autowired
    private AgentContextMapper agentContextMapper;

    @Mock
    private AgentContextService agentContextServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAgentContextMockMvc;

    private AgentContext agentContext;

    private AgentContext insertedAgentContext;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AgentContext createEntity(EntityManager em) {
        AgentContext agentContext = new AgentContext()
            .agentTaskId(DEFAULT_AGENT_TASK_ID)
            .contextType(DEFAULT_CONTEXT_TYPE)
            .contextData(DEFAULT_CONTEXT_DATA)
            .variables(DEFAULT_VARIABLES)
            .tempFiles(DEFAULT_TEMP_FILES)
            .history(DEFAULT_HISTORY)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
        // Add required entity
        AgentTask agentTask;
        if (TestUtil.findAll(em, AgentTask.class).isEmpty()) {
            agentTask = AgentTaskResourceIT.createEntity();
            em.persist(agentTask);
            em.flush();
        } else {
            agentTask = TestUtil.findAll(em, AgentTask.class).get(0);
        }
        agentContext.setAgentTask(agentTask);
        return agentContext;
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AgentContext createUpdatedEntity(EntityManager em) {
        AgentContext updatedAgentContext = new AgentContext()
            .agentTaskId(UPDATED_AGENT_TASK_ID)
            .contextType(UPDATED_CONTEXT_TYPE)
            .contextData(UPDATED_CONTEXT_DATA)
            .variables(UPDATED_VARIABLES)
            .tempFiles(UPDATED_TEMP_FILES)
            .history(UPDATED_HISTORY)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        // Add required entity
        AgentTask agentTask;
        if (TestUtil.findAll(em, AgentTask.class).isEmpty()) {
            agentTask = AgentTaskResourceIT.createUpdatedEntity();
            em.persist(agentTask);
            em.flush();
        } else {
            agentTask = TestUtil.findAll(em, AgentTask.class).get(0);
        }
        updatedAgentContext.setAgentTask(agentTask);
        return updatedAgentContext;
    }

    @BeforeEach
    void initTest() {
        agentContext = createEntity(em);
    }

    @AfterEach
    void cleanup() {
        if (insertedAgentContext != null) {
            agentContextRepository.delete(insertedAgentContext);
            insertedAgentContext = null;
        }
    }

    @Test
    @Transactional
    void createAgentContext() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);
        var returnedAgentContextDTO = om.readValue(
            restAgentContextMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AgentContextDTO.class
        );

        // Validate the AgentContext in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAgentContext = agentContextMapper.toEntity(returnedAgentContextDTO);
        assertAgentContextUpdatableFieldsEquals(returnedAgentContext, getPersistedAgentContext(returnedAgentContext));

        insertedAgentContext = returnedAgentContext;
    }

    @Test
    @Transactional
    void createAgentContextWithExistingId() throws Exception {
        // Create the AgentContext with an existing ID
        agentContext.setId(1L);
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAgentContextMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkAgentTaskIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentContext.setAgentTaskId(null);

        // Create the AgentContext, which fails.
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        restAgentContextMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentContext.setVersion(null);

        // Create the AgentContext, which fails.
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        restAgentContextMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentContext.setCreatedAt(null);

        // Create the AgentContext, which fails.
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        restAgentContextMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentContext.setUpdatedAt(null);

        // Create the AgentContext, which fails.
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        restAgentContextMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentContext.setIsDeleted(null);

        // Create the AgentContext, which fails.
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        restAgentContextMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAgentContexts() throws Exception {
        // Initialize the database
        insertedAgentContext = agentContextRepository.saveAndFlush(agentContext);

        // Get all the agentContextList
        restAgentContextMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(agentContext.getId().intValue())))
            .andExpect(jsonPath("$.[*].agentTaskId").value(hasItem(DEFAULT_AGENT_TASK_ID.intValue())))
            .andExpect(jsonPath("$.[*].contextType").value(hasItem(DEFAULT_CONTEXT_TYPE)))
            .andExpect(jsonPath("$.[*].contextData").value(hasItem(DEFAULT_CONTEXT_DATA)))
            .andExpect(jsonPath("$.[*].variables").value(hasItem(DEFAULT_VARIABLES)))
            .andExpect(jsonPath("$.[*].tempFiles").value(hasItem(DEFAULT_TEMP_FILES)))
            .andExpect(jsonPath("$.[*].history").value(hasItem(DEFAULT_HISTORY)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllAgentContextsWithEagerRelationshipsIsEnabled() throws Exception {
        when(agentContextServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restAgentContextMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(agentContextServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllAgentContextsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(agentContextServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restAgentContextMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(agentContextRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getAgentContext() throws Exception {
        // Initialize the database
        insertedAgentContext = agentContextRepository.saveAndFlush(agentContext);

        // Get the agentContext
        restAgentContextMockMvc
            .perform(get(ENTITY_API_URL_ID, agentContext.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(agentContext.getId().intValue()))
            .andExpect(jsonPath("$.agentTaskId").value(DEFAULT_AGENT_TASK_ID.intValue()))
            .andExpect(jsonPath("$.contextType").value(DEFAULT_CONTEXT_TYPE))
            .andExpect(jsonPath("$.contextData").value(DEFAULT_CONTEXT_DATA))
            .andExpect(jsonPath("$.variables").value(DEFAULT_VARIABLES))
            .andExpect(jsonPath("$.tempFiles").value(DEFAULT_TEMP_FILES))
            .andExpect(jsonPath("$.history").value(DEFAULT_HISTORY))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAgentContext() throws Exception {
        // Get the agentContext
        restAgentContextMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAgentContext() throws Exception {
        // Initialize the database
        insertedAgentContext = agentContextRepository.saveAndFlush(agentContext);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentContext
        AgentContext updatedAgentContext = agentContextRepository.findById(agentContext.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAgentContext are not directly saved in db
        em.detach(updatedAgentContext);
        updatedAgentContext
            .agentTaskId(UPDATED_AGENT_TASK_ID)
            .contextType(UPDATED_CONTEXT_TYPE)
            .contextData(UPDATED_CONTEXT_DATA)
            .variables(UPDATED_VARIABLES)
            .tempFiles(UPDATED_TEMP_FILES)
            .history(UPDATED_HISTORY)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(updatedAgentContext);

        restAgentContextMockMvc
            .perform(
                put(ENTITY_API_URL_ID, agentContextDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentContextDTO))
            )
            .andExpect(status().isOk());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAgentContextToMatchAllProperties(updatedAgentContext);
    }

    @Test
    @Transactional
    void putNonExistingAgentContext() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentContext.setId(longCount.incrementAndGet());

        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAgentContextMockMvc
            .perform(
                put(ENTITY_API_URL_ID, agentContextDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentContextDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAgentContext() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentContext.setId(longCount.incrementAndGet());

        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentContextMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentContextDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAgentContext() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentContext.setId(longCount.incrementAndGet());

        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentContextMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAgentContextWithPatch() throws Exception {
        // Initialize the database
        insertedAgentContext = agentContextRepository.saveAndFlush(agentContext);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentContext using partial update
        AgentContext partialUpdatedAgentContext = new AgentContext();
        partialUpdatedAgentContext.setId(agentContext.getId());

        partialUpdatedAgentContext
            .contextType(UPDATED_CONTEXT_TYPE)
            .variables(UPDATED_VARIABLES)
            .createdBy(UPDATED_CREATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restAgentContextMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAgentContext.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAgentContext))
            )
            .andExpect(status().isOk());

        // Validate the AgentContext in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAgentContextUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedAgentContext, agentContext),
            getPersistedAgentContext(agentContext)
        );
    }

    @Test
    @Transactional
    void fullUpdateAgentContextWithPatch() throws Exception {
        // Initialize the database
        insertedAgentContext = agentContextRepository.saveAndFlush(agentContext);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentContext using partial update
        AgentContext partialUpdatedAgentContext = new AgentContext();
        partialUpdatedAgentContext.setId(agentContext.getId());

        partialUpdatedAgentContext
            .agentTaskId(UPDATED_AGENT_TASK_ID)
            .contextType(UPDATED_CONTEXT_TYPE)
            .contextData(UPDATED_CONTEXT_DATA)
            .variables(UPDATED_VARIABLES)
            .tempFiles(UPDATED_TEMP_FILES)
            .history(UPDATED_HISTORY)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAgentContextMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAgentContext.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAgentContext))
            )
            .andExpect(status().isOk());

        // Validate the AgentContext in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAgentContextUpdatableFieldsEquals(partialUpdatedAgentContext, getPersistedAgentContext(partialUpdatedAgentContext));
    }

    @Test
    @Transactional
    void patchNonExistingAgentContext() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentContext.setId(longCount.incrementAndGet());

        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAgentContextMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, agentContextDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(agentContextDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAgentContext() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentContext.setId(longCount.incrementAndGet());

        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentContextMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(agentContextDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAgentContext() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentContext.setId(longCount.incrementAndGet());

        // Create the AgentContext
        AgentContextDTO agentContextDTO = agentContextMapper.toDto(agentContext);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentContextMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(agentContextDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AgentContext in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAgentContext() throws Exception {
        // Initialize the database
        insertedAgentContext = agentContextRepository.saveAndFlush(agentContext);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the agentContext
        restAgentContextMockMvc
            .perform(delete(ENTITY_API_URL_ID, agentContext.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return agentContextRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AgentContext getPersistedAgentContext(AgentContext agentContext) {
        return agentContextRepository.findById(agentContext.getId()).orElseThrow();
    }

    protected void assertPersistedAgentContextToMatchAllProperties(AgentContext expectedAgentContext) {
        assertAgentContextAllPropertiesEquals(expectedAgentContext, getPersistedAgentContext(expectedAgentContext));
    }

    protected void assertPersistedAgentContextToMatchUpdatableProperties(AgentContext expectedAgentContext) {
        assertAgentContextAllUpdatablePropertiesEquals(expectedAgentContext, getPersistedAgentContext(expectedAgentContext));
    }
}
