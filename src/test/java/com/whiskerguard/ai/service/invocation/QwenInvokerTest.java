package com.whiskerguard.ai.service.invocation;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.ToolStatus;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.util.RateLimitHandler;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 通义千问调用器测试类
 */
@ExtendWith(MockitoExtension.class)
class QwenInvokerTest {

    @Mock
    private WebClient.Builder webClientBuilder;

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private RagHelper ragHelper;

    @Mock
    private RateLimitHandler rateLimitHandler;

    private ObjectMapper objectMapper;

    private QwenInvoker qwenInvoker;

    private AiTool aiTool;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        qwenInvoker = new QwenInvoker(webClientBuilder, ragHelper, rateLimitHandler, objectMapper);

        // 设置 RateLimitHandler 的默认行为（使用lenient模式避免不必要的stubbing错误）
        lenient().when(rateLimitHandler.shouldDelayCall(anyString())).thenReturn(0L);
        lenient().when(rateLimitHandler.isRateLimitError(any())).thenReturn(false);
        lenient().doNothing().when(rateLimitHandler).recordSuccessfulCall(anyString());

        // 设置测试用的 AiTool
        aiTool = new AiTool();
        aiTool.setId(2003L);
        aiTool.setTenantId(1L);
        aiTool.setName("通义千问");
        aiTool.setToolKey("qwen");
        aiTool.setApiUrl("https://dashscope.aliyuncs.com/compatible-mode/v1");
        aiTool.setApiKey("sk-test-api-key");
        aiTool.setAuthType("Bearer");
        aiTool.setPath("/chat/completions");
        aiTool.setStatus(ToolStatus.AVAILABLE);
        aiTool.setWeight(100);
        aiTool.setMaxConcurrentCalls(10);
        aiTool.setIsModel(true);
        aiTool.setModelCategory("general");
        aiTool.setModelProvider("Alibaba");
        aiTool.setMetadata("{\"supports_streaming\":true,\"default_model\":\"qwen-plus\"}");
    }

    @Test
    void testGetToolKey() {
        assertThat(qwenInvoker.getToolKey()).isEqualTo("qwen");
    }

    @Test
    void testSupportsStreaming() {
        assertThat(qwenInvoker.supportsStreaming()).isTrue();
    }

    @Test
    void testInvokeSuccess() {
        // 准备测试数据
        AiInvocationRequestDTO dto = new AiInvocationRequestDTO();
        dto.setToolKey("qwen");
        dto.setPrompt("你好");
        dto.setTenantId(1L);
        dto.setEmployeeId(1L);

        // 模拟 RAG 增强
        when(ragHelper.enhancePromptWithRag(any(AiInvocationRequestDTO.class))).thenReturn("增强后的提示词：你好");

        // 模拟 WebClient 调用链
        when(webClientBuilder.baseUrl(anyString())).thenReturn(webClientBuilder);
        when(webClientBuilder.defaultHeader(anyString(), anyString())).thenReturn(webClientBuilder);
        when(webClientBuilder.build()).thenReturn(webClient);
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        //when(requestBodySpec.bodyValue(any())).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);

        // 模拟 API 响应
        Map<String, Object> mockResponse = createMockResponse();
        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(mockResponse));

        // 执行测试
        AiResult result = qwenInvoker.invoke(dto, aiTool);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo("你好！我是通义千问，很高兴为您服务。");
        assertThat(result.getUsage()).isNotNull();
        assertThat(result.getDurationMs()).isGreaterThan(0);
    }

    @Test
    void testInvokeWithMetadata() {
        // 准备测试数据
        AiInvocationRequestDTO dto = new AiInvocationRequestDTO();
        dto.setToolKey("qwen");
        dto.setPrompt("请介绍一下人工智能");
        dto.setTenantId(1L);
        dto.setEmployeeId(1L);

        // 添加元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("temperature", 0.8);
        metadata.put("max_tokens", 1000);
        metadata.put("top_p", 0.9);
        dto.setMetadata(metadata);

        // 模拟 RAG 增强
        when(ragHelper.enhancePromptWithRag(any(AiInvocationRequestDTO.class))).thenReturn("增强后的提示词：请介绍一下人工智能");

        // 模拟 WebClient 调用链
        when(webClientBuilder.baseUrl(anyString())).thenReturn(webClientBuilder);
        when(webClientBuilder.defaultHeader(anyString(), anyString())).thenReturn(webClientBuilder);
        when(webClientBuilder.build()).thenReturn(webClient);
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        //when(requestBodySpec.bodyValue(any())).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);

        // 模拟 API 响应
        Map<String, Object> mockResponse = createMockResponse();
        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(mockResponse));

        // 执行测试
        AiResult result = qwenInvoker.invoke(dto, aiTool);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isNotEmpty();
    }

    @Test
    void testInvokeWithError() {
        // 准备测试数据
        AiInvocationRequestDTO dto = new AiInvocationRequestDTO();
        dto.setToolKey("qwen");
        dto.setPrompt("测试错误");
        dto.setTenantId(1L);
        dto.setEmployeeId(1L);

        // 模拟 RAG 增强
        when(ragHelper.enhancePromptWithRag(any(AiInvocationRequestDTO.class))).thenReturn("增强后的提示词：测试错误");

        // 模拟 WebClient 调用链
        when(webClientBuilder.baseUrl(anyString())).thenReturn(webClientBuilder);
        when(webClientBuilder.defaultHeader(anyString(), anyString())).thenReturn(webClientBuilder);
        when(webClientBuilder.build()).thenReturn(webClient);
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        //when(requestBodySpec.bodyValue(any())).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);

        // 模拟 API 错误
        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.error(new RuntimeException("API调用失败")));

        // 执行测试
        AiResult result = qwenInvoker.invoke(dto, aiTool);

        // 验证错误处理
        assertThat(result).isNotNull();
        assertThat(result.getContent()).contains("调用通义千问 API失败");
        assertThat(result.getUsage()).containsKey("error");
    }

    /**
     * 创建模拟的 API 响应
     */
    private Map<String, Object> createMockResponse() {
        Map<String, Object> response = new HashMap<>();

        // 创建 choices
        Map<String, Object> choice = new HashMap<>();
        Map<String, Object> message = new HashMap<>();
        message.put("role", "assistant");
        message.put("content", "你好！我是通义千问，很高兴为您服务。");
        choice.put("message", message);
        choice.put("finish_reason", "stop");
        response.put("choices", List.of(choice));

        // 创建 usage
        Map<String, Object> usage = new HashMap<>();
        usage.put("input_tokens", 10);
        usage.put("output_tokens", 20);
        usage.put("total_tokens", 30);
        response.put("usage", usage);

        return response;
    }
}
