package com.whiskerguard.ai.service.invocation;

import static org.junit.jupiter.api.Assertions.*;

import com.whiskerguard.ai.service.util.RateLimitHandler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 速率限制优化集成测试
 * <p>
 * 验证所有AI服务调用器都已正确集成RateLimitHandler，
 * 确保优化方案的完整性和一致性。
 *
 * <AUTHOR> Guard AI Team
 */
@SpringBootTest
@ActiveProfiles("test")
class RateLimitOptimizationIntegrationTest {

    @Autowired
    private RateLimitHandler rateLimitHandler;

    @Autowired(required = false)
    private KimiInvoker kimiInvoker;

    @Autowired(required = false)
    private DouBaoInvoker douBaoInvoker;

    @Autowired(required = false)
    private ClaudeInvoker claudeInvoker;

    @Autowired(required = false)
    private QwenInvoker qwenInvoker;

    @Autowired(required = false)
    private DeepSeekInvoker deepSeekInvoker;

    @Autowired(required = false)
    private ChatGptInvoker chatGptInvoker;

    @Autowired(required = false)
    private LaWGPTInvoker laWGPTInvoker;

    @Autowired(required = false)
    private ChatLawInvoker chatLawInvoker;

    /**
     * 测试RateLimitHandler是否正确注入
     */
    @Test
    void testRateLimitHandlerInjection() {
        assertNotNull(rateLimitHandler, "RateLimitHandler应该被正确注入");
    }

    /**
     * 测试所有Invoker是否正确注入
     */
    @Test
    void testAllInvokersAreInjected() {
        // 核心Invoker（高优先级）
        if (kimiInvoker != null) {
            assertNotNull(kimiInvoker, "KimiInvoker应该被正确注入");
        }
        if (douBaoInvoker != null) {
            assertNotNull(douBaoInvoker, "DouBaoInvoker应该被正确注入");
        }
        if (claudeInvoker != null) {
            assertNotNull(claudeInvoker, "ClaudeInvoker应该被正确注入");
        }
        if (qwenInvoker != null) {
            assertNotNull(qwenInvoker, "QwenInvoker应该被正确注入");
        }
        if (deepSeekInvoker != null) {
            assertNotNull(deepSeekInvoker, "DeepSeekInvoker应该被正确注入");
        }

        // 其他Invoker（中优先级）
        if (chatGptInvoker != null) {
            assertNotNull(chatGptInvoker, "ChatGptInvoker应该被正确注入");
        }
        if (laWGPTInvoker != null) {
            assertNotNull(laWGPTInvoker, "LaWGPTInvoker应该被正确注入");
        }
        if (chatLawInvoker != null) {
            assertNotNull(chatLawInvoker, "ChatLawInvoker应该被正确注入");
        }
    }

    /**
     * 测试RateLimitHandler的基本功能
     */
    @Test
    void testRateLimitHandlerBasicFunctionality() {
        String testApiKey = "test-api";

        // 测试初始状态
        long initialDelay = rateLimitHandler.shouldDelayCall(testApiKey);
        assertEquals(0, initialDelay, "初始调用不应该有延迟");

        // 测试成功调用记录
        assertDoesNotThrow(() -> rateLimitHandler.recordSuccessfulCall(testApiKey));

        // 测试错误记录
        Exception testException = new RuntimeException("429 Too Many Requests");
        assertTrue(rateLimitHandler.isRateLimitError(testException), "应该正确识别速率限制错误");

        assertDoesNotThrow(() -> rateLimitHandler.recordRateLimitError(testApiKey));

        // 测试退避时间计算
        long backoffTime = rateLimitHandler.calculateBackoffTime(testApiKey, 1);
        assertTrue(backoffTime > 0, "退避时间应该大于0");

        // 测试错误建议
        String advice = rateLimitHandler.getErrorAdvice(testApiKey, testException);
        assertNotNull(advice, "应该提供错误建议");
        assertTrue(advice.contains("速率限制"), "错误建议应该包含速率限制相关信息");
    }

    /**
     * 测试不同API的退避时间配置
     */
    @Test
    void testDifferentApiBackoffTimes() {
        // 测试Kimi API（应该有最长的退避时间）
        long kimiBackoff = rateLimitHandler.calculateBackoffTime("kimi", 1);

        // 测试其他API
        long doubaoBackoff = rateLimitHandler.calculateBackoffTime("doubao", 1);
        long claudeBackoff = rateLimitHandler.calculateBackoffTime("claude", 1);
        long qwenBackoff = rateLimitHandler.calculateBackoffTime("qwen", 1);

        // Kimi的退避时间应该最长（基础5秒 vs 其他3秒）
        assertTrue(kimiBackoff >= doubaoBackoff, "Kimi的退避时间应该不少于豆包");
        assertTrue(kimiBackoff >= claudeBackoff, "Kimi的退避时间应该不少于Claude");
        assertTrue(kimiBackoff >= qwenBackoff, "Kimi的退避时间应该不少于通义千问");
    }

    /**
     * 测试连续错误的影响
     */
    @Test
    void testConsecutiveErrorsImpact() {
        String testApiKey = "consecutive-test";
        Exception rateLimitError = new RuntimeException("429 Too Many Requests");

        // 记录第一次错误
        rateLimitHandler.recordRateLimitError(testApiKey);
        long firstBackoff = rateLimitHandler.calculateBackoffTime(testApiKey, 1);

        // 记录第二次错误
        rateLimitHandler.recordRateLimitError(testApiKey);
        long secondBackoff = rateLimitHandler.calculateBackoffTime(testApiKey, 1);

        // 连续错误应该增加退避时间
        assertTrue(secondBackoff > firstBackoff, "连续错误应该增加退避时间");

        // 成功调用应该重置错误计数
        rateLimitHandler.recordSuccessfulCall(testApiKey);
        long resetBackoff = rateLimitHandler.calculateBackoffTime(testApiKey, 1);
        assertTrue(resetBackoff < secondBackoff, "成功调用后退避时间应该减少");
    }

    /**
     * 测试清理功能
     */
    @Test
    void testCleanupFunctionality() {
        // 清理操作不应该抛出异常
        assertDoesNotThrow(() -> rateLimitHandler.cleanupExpiredRecords());
    }

    /**
     * 测试错误识别的准确性
     */
    @Test
    void testErrorRecognitionAccuracy() {
        // 应该识别的错误
        assertTrue(rateLimitHandler.isRateLimitError(new RuntimeException("429 Too Many Requests")));
        assertTrue(rateLimitHandler.isRateLimitError(new RuntimeException("Rate limit exceeded")));
        assertTrue(rateLimitHandler.isRateLimitError(new RuntimeException("请求过于频繁")));

        // 不应该识别的错误
        assertFalse(rateLimitHandler.isRateLimitError(new RuntimeException("500 Internal Server Error")));
        assertFalse(rateLimitHandler.isRateLimitError(new RuntimeException("404 Not Found")));
        assertFalse(rateLimitHandler.isRateLimitError(null));
    }

    /**
     * 测试最大退避时间限制
     */
    @Test
    void testMaxBackoffTimeLimit() {
        String testApiKey = "max-backoff-test";

        // 测试极大的重试次数
        long maxBackoff = rateLimitHandler.calculateBackoffTime(testApiKey, 10);

        // 应该不超过60秒（60000毫秒）
        assertTrue(maxBackoff <= 60000, "退避时间不应该超过60秒");
    }
}
