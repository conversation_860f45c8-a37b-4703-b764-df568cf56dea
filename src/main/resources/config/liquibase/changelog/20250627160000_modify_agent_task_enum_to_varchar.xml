<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!--
        将 AgentTask 表的枚举字段改为 VARCHAR 类型
        解决 Hibernate 枚举映射问题，提高兼容性
    -->
    <changeSet id="20250627160000-1" author="jhipster">
        <comment>将 AgentTask 表的 task_type、status、priority 字段从 ENUM 改为 VARCHAR</comment>

        <!-- 修改 task_type 字段为 VARCHAR(50) -->
        <modifyDataType tableName="agent_task" columnName="task_type" newDataType="varchar(50)"/>

        <!-- 修改 status 字段为 VARCHAR(20) -->
        <modifyDataType tableName="agent_task" columnName="status" newDataType="varchar(20)"/>

        <!-- 修改 priority 字段为 VARCHAR(20) -->
        <modifyDataType tableName="agent_task" columnName="priority" newDataType="varchar(20)"/>
    </changeSet>

    <!--
        为了保证数据完整性，添加检查约束（可选）
    -->
    <changeSet id="20250627160000-2" author="jhipster">
        <comment>为 AgentTask 表添加检查约束，确保字段值的有效性</comment>

        <!-- 为 task_type 添加检查约束 -->
        <sql>
            ALTER TABLE agent_task
            ADD CONSTRAINT chk_agent_task_type
            CHECK (task_type IN ('REGULATION_INTERNALIZATION', 'POLICY_REVIEW', 'CONTRACT_REVIEW'))
        </sql>

        <!-- 为 status 添加检查约束 -->
        <sql>
            ALTER TABLE agent_task
            ADD CONSTRAINT chk_agent_task_status
            CHECK (status IN ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED'))
        </sql>

        <!-- 为 priority 添加检查约束 -->
        <sql>
            ALTER TABLE agent_task
            ADD CONSTRAINT chk_agent_task_priority
            CHECK (priority IN ('HIGH', 'LOW', 'NORMAL', 'URGENT'))
        </sql>
    </changeSet>

</databaseChangeLog>
