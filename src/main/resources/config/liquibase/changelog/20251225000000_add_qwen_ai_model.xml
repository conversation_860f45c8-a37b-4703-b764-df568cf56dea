<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Add Qwen (通义千问) AI model to ai_tool table
    -->
    <changeSet id="20251225000000-1" author="developer">
        <comment>Add <PERSON>wen (通义千问) AI model configuration</comment>

        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="1">
                    SELECT COUNT(*) FROM ai_tool WHERE id = 2003 OR tool_key = 'qwen'
                </sqlCheck>
            </not>
        </preConditions>

        <!-- Insert Qwen configuration -->
        <insert tableName="ai_tool">
            <column name="id" valueNumeric="2003"/>
            <column name="tenant_id" valueNumeric="1"/>
            <column name="name" value="通义千问"/>
            <column name="tool_key" value="qwen"/>
            <column name="version" valueNumeric="1"/>
            <column name="api_url" value="https://dashscope.aliyuncs.com/compatible-mode/v1"/>
            <column name="api_key" value="sk-your-dashscope-api-key"/>
            <column name="auth_type" value="Bearer"/>
            <column name="path" value="/chat/completions"/>
            <column name="status" value="AVAILABLE"/>
            <column name="weight" valueNumeric="100"/>
            <column name="max_concurrent_calls" valueNumeric="10"/>
            <column name="is_model" valueBoolean="true"/>
            <column name="model_category" value="general"/>
            <column name="model_provider" value="Alibaba"/>
            <column name="remark" value="阿里云通义千问大语言模型，支持多种模型规格（qwen-plus、qwen-max、qwen-turbo等），具备强大的中文理解和生成能力"/>
            <column name="metadata" value="{&quot;supports_streaming&quot;:true,&quot;model_type&quot;:&quot;general&quot;,&quot;language&quot;:&quot;zh-CN&quot;,&quot;default_model&quot;:&quot;qwen-plus&quot;,&quot;available_models&quot;:[&quot;qwen-plus&quot;,&quot;qwen-max&quot;,&quot;qwen-turbo&quot;,&quot;qwen-long&quot;],&quot;max_tokens&quot;:8192,&quot;temperature&quot;:0.7,&quot;top_p&quot;:0.8}"/>
            <column name="created_by" value="system"/>
            <column name="created_at" valueDate="2024-12-25T00:00:00"/>
            <column name="updated_by" value="system"/>
            <column name="updated_at" valueDate="2024-12-25T00:00:00"/>
            <column name="is_deleted" valueBoolean="false"/>
        </insert>
    </changeSet>

</databaseChangeLog>
