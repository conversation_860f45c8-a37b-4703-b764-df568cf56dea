<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AgentConfig.
    -->
    <changeSet id="20250619033816-1" author="jhipster">
        <createTable tableName="agent_config" remarks="Agent配置实体\n存储Agent的配置参数">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID (为空时表示全局配置)">
                <constraints nullable="true" />
            </column>
            <column name="config_key" type="varchar(100)" remarks="配置键">
                <constraints nullable="false" />
            </column>
            <column name="config_value" type="varchar(2000)" remarks="配置值">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(500)" remarks="配置描述">
                <constraints nullable="true" />
            </column>
            <column name="config_group" type="varchar(50)" remarks="配置分组">
                <constraints nullable="true" />
            </column>
            <column name="enabled" type="boolean" remarks="是否启用">
                <constraints nullable="false" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="agent_config" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="agent_config" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619033816-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/agent_config.csv"
                  separator=";"
                  tableName="agent_config"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="config_key" type="string"/>
            <column name="config_value" type="string"/>
            <column name="description" type="string"/>
            <column name="config_group" type="string"/>
            <column name="enabled" type="boolean"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
