<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Add ChatLaw and LaWGPT legal AI models to ai_tool table
    -->
    <changeSet id="20251221000000-1" author="developer">
        <comment>Add ChatLaw and LaWGPT legal AI models configuration</comment>

        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="1">
                    SELECT COUNT(*) FROM ai_tool WHERE id IN (2001, 2002) OR tool_key IN ('chatlaw', 'lawgpt')
                </sqlCheck>
            </not>
        </preConditions>

        <!-- Insert ChatLaw configuration -->
        <insert tableName="ai_tool">
            <column name="id" valueNumeric="2001"/>
            <column name="tenant_id" valueNumeric="1"/>
            <column name="name" value="ChatLaw"/>
            <column name="tool_key" value="chatlaw"/>
            <column name="version" valueNumeric="1"/>
            <column name="api_url" value="https://api.chatlaw.cloud"/>
            <column name="api_key" value="your-chatlaw-api-key"/>
            <column name="auth_type" value="Bearer"/>
            <column name="path" value="/v1/chat/completions"/>
            <column name="status" value="AVAILABLE"/>
            <column name="weight" valueNumeric="100"/>
            <column name="max_concurrent_calls" valueNumeric="10"/>
            <column name="is_model" valueBoolean="true"/>
            <column name="model_category" value="legal"/>
            <column name="model_provider" value="ChatLaw"/>
            <column name="remark" value="ChatLaw法律专业模型，专注于法律文本分析和政策生成"/>
            <column name="metadata" value="{&quot;supports_streaming&quot;:false,&quot;model_type&quot;:&quot;legal&quot;,&quot;language&quot;:&quot;zh-CN&quot;}"/>
            <column name="created_by" value="system"/>
            <column name="created_at" valueDate="2024-12-21T00:00:00"/>
            <column name="updated_by" value="system"/>
            <column name="updated_at" valueDate="2024-12-21T00:00:00"/>
            <column name="is_deleted" valueBoolean="false"/>
        </insert>

        <!-- Insert LaWGPT configuration -->
        <insert tableName="ai_tool">
            <column name="id" valueNumeric="2002"/>
            <column name="tenant_id" valueNumeric="1"/>
            <column name="name" value="LaWGPT"/>
            <column name="tool_key" value="lawgpt"/>
            <column name="version" valueNumeric="1"/>
            <column name="api_url" value="http://localhost:7860"/>
            <column name="api_key" value="lawgpt-local-key"/>
            <column name="auth_type" value="Custom"/>
            <column name="path" value="/api/predict"/>
            <column name="status" value="AVAILABLE"/>
            <column name="weight" valueNumeric="100"/>
            <column name="max_concurrent_calls" valueNumeric="5"/>
            <column name="is_model" valueBoolean="true"/>
            <column name="model_category" value="legal"/>
            <column name="model_provider" value="LaWGPT"/>
            <column name="remark" value="LaWGPT本地部署法律模型，专业法律知识问答和政策分析"/>
            <column name="metadata" value="{&quot;supports_streaming&quot;:false,&quot;model_type&quot;:&quot;legal&quot;,&quot;language&quot;:&quot;zh-CN&quot;,&quot;max_length&quot;:2000}"/>
            <column name="created_by" value="system"/>
            <column name="created_at" valueDate="2024-12-21T00:00:00"/>
            <column name="updated_by" value="system"/>
            <column name="updated_at" valueDate="2024-12-21T00:00:00"/>
            <column name="is_deleted" valueBoolean="false"/>
        </insert>
    </changeSet>

</databaseChangeLog>
