<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Modify agent_context table to change variables column to TEXT type
        修改 agent_context 表，将 variables 列改为 TEXT 类型
    -->
    <changeSet id="20250627190000-modify-agent-context-variables-column" author="yanhaishui">
        <comment>Modify agent_context.variables column to TEXT type to support longer data</comment>

        <!-- MySQL/MariaDB: Modify column to TEXT -->
        <modifyDataType tableName="agent_context"
                       columnName="variables"
                       newDataType="TEXT"/>

        <!-- Also ensure context_data, temp_files, and history columns are TEXT for consistency -->
        <modifyDataType tableName="agent_context"
                       columnName="context_data"
                       newDataType="TEXT"/>

        <modifyDataType tableName="agent_context"
                       columnName="temp_files"
                       newDataType="TEXT"/>

        <modifyDataType tableName="agent_context"
                       columnName="history"
                       newDataType="TEXT"/>
    </changeSet>

</databaseChangeLog>
