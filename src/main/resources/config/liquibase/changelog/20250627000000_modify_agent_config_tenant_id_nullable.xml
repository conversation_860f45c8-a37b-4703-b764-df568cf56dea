<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        修改 AgentConfig 表的 tenant_id 字段允许为空，以支持全局配置
        Modify AgentConfig table tenant_id column to allow null values for global configurations
    -->
    <changeSet id="20250627000000-1" author="yanhaishui">
        <modifyDataType
            tableName="agent_config"
            columnName="tenant_id"
            newDataType="bigint"/>
        <dropNotNullConstraint
            tableName="agent_config"
            columnName="tenant_id"
            columnDataType="bigint"/>
        <setColumnRemarks
            tableName="agent_config"
            columnName="tenant_id"
            columnDataType="bigint"
            remarks="租户 ID (为空时表示全局配置，适用于所有租户) / Tenant ID (null indicates global configuration applicable to all tenants)"/>
    </changeSet>

</databaseChangeLog>
