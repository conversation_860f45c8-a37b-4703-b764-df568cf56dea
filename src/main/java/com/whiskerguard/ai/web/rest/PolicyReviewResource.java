/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：内部制度审查REST控制器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewResponseDTO;
import com.whiskerguard.ai.service.policy.InternalPolicyReviewService;
import com.whiskerguard.ai.util.LlmResponseProcessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.RequestAttributes;

/**
 * 内部制度审查
 * <p>
 * 提供内部制度智能审查的REST API接口，供法规微服务调用。
 * 该控制器负责接收制度审查请求，调用相应的服务进行处理，
 * 并返回详细的审查结果。
 *
 * 主要功能：
 * 1. 接收内部制度审查请求
 * 2. 参数验证和预处理
 * 3. 调用审查服务进行分析
 * 4. 返回结构化的审查结果
 * 5. 异常处理和错误响应
 */
@Tag(name = "内部制度审查", description = "内部制度智能审查API")
@RestController
@RequestMapping("/api/policy-review")
public class PolicyReviewResource {

    private static final Logger log = LoggerFactory.getLogger(PolicyReviewResource.class);

    private final InternalPolicyReviewService internalPolicyReviewService;

    public PolicyReviewResource(InternalPolicyReviewService internalPolicyReviewService) {
        this.internalPolicyReviewService = internalPolicyReviewService;
    }

    /**
     * 内部制度智能审查接口
     * <p>
     * POST /api/policy-review/internal : 对内部制度进行智能审查分析。
     * 该接口接收制度内容和相关参数，通过AI模型和多数据源整合，
     * 提供全面的制度风险评估、合规性检查和改进建议。
     *
     * @param request 内部制度审查请求DTO，包含制度内容、类型、企业信息等
     * @return 包含详细审查结果的ResponseEntity，状态码200(OK)，响应体为InternalPolicyReviewResponseDTO
     */
    @Operation(summary = "内部制度智能审查", description = "对企业内部制度进行全面的智能审查，包括风险识别、合规检查、条款分析和改进建议")
    @ApiResponses(
        value = {
            @ApiResponse(
                responseCode = "200",
                description = "审查成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = InternalPolicyReviewResponseDTO.class))
            ),
            @ApiResponse(responseCode = "400", description = "请求参数错误", content = @Content),
            @ApiResponse(responseCode = "401", description = "未授权访问", content = @Content),
            @ApiResponse(responseCode = "403", description = "权限不足", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
            @ApiResponse(responseCode = "503", description = "服务暂时不可用", content = @Content),
        }
    )
    @PostMapping("/internal")
    public ResponseEntity<InternalPolicyReviewResponseDTO> reviewInternalPolicy(
        @Parameter(description = "内部制度审查请求", required = true) @Valid @RequestBody InternalPolicyReviewRequestDTO request
    ) {
        // 从 metadata 中获取AI模型选择
        String aiModel = "kimi"; // 默认模型
        if (request.getMetadata() != null && request.getMetadata().containsKey("aiModel")) {
            String specifiedModel = (String) request.getMetadata().get("aiModel");
            aiModel = LlmResponseProcessor.normalizeModelName(specifiedModel);
        }

        log.info(
            "收到内部制度审查请求，租户ID: {}, 制度类型: {}, 员工ID: {}, AI模型: {}",
            request.getTenantId(),
            request.getPolicyType(),
            request.getEmployeeId(),
            aiModel
        );

        try {
            // 记录请求的基本信息（不记录敏感内容）
            log.debug(
                "制度审查请求详情 - 制度标题: {}, 制定部门: {}, 公司名称: {}, 行业: {}, 优先级: {}",
                request.getPolicyTitle(),
                request.getDepartment(),
                request.getCompanyName(),
                request.getIndustry(),
                request.getPriority()
            );

            // 调用服务进行制度审查
            InternalPolicyReviewResponseDTO response = internalPolicyReviewService.reviewInternalPolicy(request);

            // 记录审查结果摘要
            log.info(
                "内部制度审查完成，租户ID: {}, 审查ID: {}, 风险等级: {}, 风险分数: {}, 耗时: {}ms",
                request.getTenantId(),
                response.getReviewId(),
                response.getOverallRiskLevel(),
                response.getRiskScore(),
                response.getReviewDuration()
            );

            // 记录关键指标
            if (response.getRiskPoints() != null) {
                log.debug("识别风险点数量: {}", response.getRiskPoints().size());
            }
            if (response.getClauseIssues() != null) {
                log.debug("发现条款问题数量: {}", response.getClauseIssues().size());
            }
            if (response.getRelatedPartyRisks() != null) {
                log.debug("关联方风险数量: {}", response.getRelatedPartyRisks().size());
            }

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.warn("内部制度审查请求参数错误，租户ID: {}, 错误: {}", request.getTenantId(), e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (SecurityException e) {
            log.warn("内部制度审查权限不足，租户ID: {}, 员工ID: {}", request.getTenantId(), request.getEmployeeId());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        } catch (Exception e) {
            log.error(
                "内部制度审查失败，租户ID: {}, 制度类型: {}, 错误: {}",
                request.getTenantId(),
                request.getPolicyType(),
                e.getMessage(),
                e
            );
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 健康检查接口
     * <p>
     * GET /api/policy-review/health : 检查制度审查服务的健康状态。
     * 用于监控和负载均衡器检查服务可用性。
     *
     * @return 服务健康状态
     */
    @Operation(summary = "健康检查", description = "检查内部制度审查服务的健康状态")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "服务正常", content = @Content),
            @ApiResponse(responseCode = "503", description = "服务不可用", content = @Content),
        }
    )
    @PostMapping("/health")
    public ResponseEntity<String> healthCheck() {
        try {
            // 这里可以添加更复杂的健康检查逻辑
            // 例如检查依赖服务的可用性、数据库连接等
            log.debug("内部制度审查服务健康检查");
            return ResponseEntity.ok("OK");
        } catch (Exception e) {
            log.error("内部制度审查服务健康检查失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body("Service Unavailable");
        }
    }

    /**
     * 获取支持的制度类型列表
     * <p>
     * GET /api/policy-review/supported-types : 获取当前支持的制度类型列表。
     * 用于前端界面显示可选的制度类型。
     *
     * @return 支持的制度类型列表
     */
    @Operation(summary = "获取支持的制度类型", description = "获取当前系统支持的内部制度类型列表")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
        }
    )
    @PostMapping("/supported-types")
    public ResponseEntity<String[]> getSupportedPolicyTypes() {
        try {
            String[] supportedTypes = {
                "人事制度",
                "财务制度",
                "采购制度",
                "销售制度",
                "生产制度",
                "质量制度",
                "安全制度",
                "信息安全制度",
                "合规制度",
                "风险管理制度",
                "内控制度",
                "审计制度",
                "行政管理制度",
                "技术管理制度",
                "客户服务制度",
                "供应商管理制度",
                "项目管理制度",
                "知识产权制度",
                "环保制度",
                "其他制度",
            };

            log.debug("返回支持的制度类型列表，数量: {}", supportedTypes.length);
            return ResponseEntity.ok(supportedTypes);
        } catch (Exception e) {
            log.error("获取支持的制度类型失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取审查配置信息
     * <p>
     * GET /api/policy-review/config : 获取制度审查的配置信息。
     * 包括支持的优先级、审查重点、风险容忍度等配置选项。
     *
     * @return 审查配置信息
     */
    @Operation(summary = "获取审查配置", description = "获取内部制度审查的配置信息和选项")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
        }
    )
    @PostMapping("/config")
    public ResponseEntity<Object> getReviewConfig() {
        try {
            // 构建配置信息
            var config = new Object() {
                public final String[] priorities = { "LOW", "NORMAL", "HIGH", "URGENT" };
                public final String[] reviewFocus = { "COMPREHENSIVE", "LEGAL_ONLY", "RISK_ONLY", "OPERATIONAL_ONLY", "FINANCIAL_ONLY" };
                public final String[] riskTolerance = { "LOW", "MEDIUM", "HIGH" };
                public final int maxContentLength = 1000000;
                public final int minContentLength = 10;
                public final boolean ragEnabled = true;
                public final boolean deepAnalysisEnabled = true;
                public final boolean relatedPartyCheckEnabled = true;
                public final boolean legalComplianceCheckEnabled = true;
            };

            log.debug("返回制度审查配置信息");
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("获取审查配置失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 内部制度智能审查流式接口
     * <p>
     * POST /api/policy-review/internal/stream : 对内部制度进行智能审查分析（流式响应）。
     * 该接口使用服务器发送事件(SSE)技术，实时推送审查进度和结果，
     * 提供更好的用户体验，特别适合长时间的AI分析任务。
     *
     * @param request 内部制度审查请求DTO，包含制度内容、类型、企业信息等
     * @return SSE流式响应，实时推送审查进度和结果
     */
    @Operation(summary = "内部制度智能审查（流式）", description = "对企业内部制度进行全面的智能审查，使用流式响应实时推送进度和结果")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "开始流式审查", content = @Content(mediaType = "text/event-stream")),
            @ApiResponse(responseCode = "400", description = "请求参数错误", content = @Content),
            @ApiResponse(responseCode = "401", description = "未授权访问", content = @Content),
            @ApiResponse(responseCode = "403", description = "权限不足", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
            @ApiResponse(responseCode = "503", description = "服务暂时不可用", content = @Content),
        }
    )
    @PostMapping(value = "/internal/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter reviewInternalPolicyStream(
        @Parameter(description = "内部制度审查请求", required = true) @Valid @RequestBody InternalPolicyReviewRequestDTO request
    ) {
        // 从 metadata 中获取AI模型选择
        String aiModel = "kimi"; // 默认模型
        if (request.getMetadata() != null && request.getMetadata().containsKey("aiModel")) {
            String specifiedModel = (String) request.getMetadata().get("aiModel");
            aiModel = LlmResponseProcessor.normalizeModelName(specifiedModel);
        }

        log.info(
            "收到内部制度审查流式请求，租户ID: {}, 制度类型: {}, 员工ID: {}, AI模型: {}",
            request.getTenantId(),
            request.getPolicyType(),
            request.getEmployeeId(),
            aiModel
        );

        // 创建SSE发射器，设置超时时间为5分钟
        SseEmitter emitter = new SseEmitter(300_000L);

        // 捕获当前请求上下文，以便在异步线程中使用
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();

        // 异步处理审查请求
        CompletableFuture.runAsync(() -> {
            try {
                // 在异步线程中设置请求上下文
                RequestContextHolder.setRequestAttributes(requestAttributes);

                // 发送开始事件
                sendProgressEvent(emitter, "START", "开始制度审查分析...", 0);

                // 记录请求的基本信息（不记录敏感内容）
                log.debug(
                    "流式制度审查请求详情 - 制度标题: {}, 制定部门: {}, 公司名称: {}, 行业: {}, 优先级: {}",
                    request.getPolicyTitle(),
                    request.getDepartment(),
                    request.getCompanyName(),
                    request.getIndustry(),
                    request.getPriority()
                );

                // 阶段1：参数验证
                sendProgressEvent(emitter, "VALIDATION", "正在验证请求参数...", 10);
                Thread.sleep(500); // 模拟处理时间

                // 阶段2：制度内容分析
                sendProgressEvent(emitter, "ANALYSIS", "正在分析制度内容结构...", 20);
                Thread.sleep(1000);

                // 阶段3：获取法律法规上下文
                sendProgressEvent(emitter, "LEGAL_CONTEXT", "正在获取相关法律法规...", 35);
                Thread.sleep(1500);

                // 阶段4：关联方风险分析
                sendProgressEvent(emitter, "RELATED_PARTY", "正在分析关联方风险...", 50);
                Thread.sleep(1000);

                // 阶段5：RAG增强信息检索
                sendProgressEvent(emitter, "RAG_RETRIEVAL", "正在检索相关案例和最佳实践...", 65);
                Thread.sleep(1500);

                // 阶段6：AI智能分析
                sendProgressEvent(emitter, "AI_ANALYSIS", "正在进行AI智能分析，请耐心等待...", 75);

                // 调用实际的审查服务
                InternalPolicyReviewResponseDTO response = internalPolicyReviewService.reviewInternalPolicy(request);

                // 阶段7：结果处理
                sendProgressEvent(emitter, "RESULT_PROCESSING", "正在处理分析结果...", 90);
                Thread.sleep(500);

                // 发送最终结果
                sendResultEvent(emitter, response);

                // 发送完成事件
                sendProgressEvent(emitter, "COMPLETED", "制度审查分析完成", 100);

                // 记录审查结果摘要
                log.info(
                    "流式内部制度审查完成，租户ID: {}, 审查ID: {}, 风险等级: {}, 风险分数: {}, 耗时: {}ms",
                    request.getTenantId(),
                    response.getReviewId(),
                    response.getOverallRiskLevel(),
                    response.getRiskScore(),
                    response.getReviewDuration()
                );

                // 完成流
                emitter.complete();
            } catch (IllegalArgumentException e) {
                log.warn("流式内部制度审查请求参数错误，租户ID: {}, 错误: {}", request.getTenantId(), e.getMessage());
                sendErrorEvent(emitter, "VALIDATION_ERROR", "请求参数错误: " + e.getMessage());
            } catch (SecurityException e) {
                log.warn("流式内部制度审查权限不足，租户ID: {}, 员工ID: {}", request.getTenantId(), request.getEmployeeId());
                sendErrorEvent(emitter, "PERMISSION_ERROR", "权限不足，无法进行审查");
            } catch (Exception e) {
                log.error(
                    "流式内部制度审查失败，租户ID: {}, 制度类型: {}, 错误: {}",
                    request.getTenantId(),
                    request.getPolicyType(),
                    e.getMessage(),
                    e
                );
                sendErrorEvent(emitter, "INTERNAL_ERROR", "服务器内部错误，请稍后重试");
            } finally {
                // 清理请求上下文
                RequestContextHolder.resetRequestAttributes();
            }
        });

        // 设置超时和完成回调
        emitter.onTimeout(() -> {
            log.warn("流式内部制度审查超时，租户ID: {}", request.getTenantId());
            sendErrorEvent(emitter, "TIMEOUT", "请求处理超时，请稍后重试");
        });

        emitter.onCompletion(() -> log.debug("流式内部制度审查连接关闭，租户ID: {}", request.getTenantId()));

        emitter.onError(ex -> log.error("流式内部制度审查连接错误，租户ID: {}, 错误: {}", request.getTenantId(), ex.getMessage()));

        return emitter;
    }

    /**
     * 发送进度事件
     */
    private void sendProgressEvent(SseEmitter emitter, String stage, String message, int progress) {
        try {
            var eventData = new Object() {
                public final String type = "progress";
                public final String stageValue = stage;
                public final String messageValue = message;
                public final int progressValue = progress;
                public final long timestamp = System.currentTimeMillis();
            };

            emitter.send(SseEmitter.event().name("progress").data(eventData));
        } catch (IOException e) {
            log.error("发送进度事件失败: {}", e.getMessage());
            emitter.completeWithError(e);
        }
    }

    /**
     * 发送结果事件
     */
    private void sendResultEvent(SseEmitter emitter, InternalPolicyReviewResponseDTO result) {
        try {
            var eventData = new Object() {
                public final String type = "result";
                public final InternalPolicyReviewResponseDTO dataValue = result;
                public final long timestamp = System.currentTimeMillis();
            };

            emitter.send(SseEmitter.event().name("result").data(eventData));
        } catch (IOException e) {
            log.error("发送结果事件失败: {}", e.getMessage());
            emitter.completeWithError(e);
        }
    }

    /**
     * 发送错误事件
     */
    private void sendErrorEvent(SseEmitter emitter, String errorType, String errorMessage) {
        try {
            var eventData = new Object() {
                public final String type = "error";
                public final String errorTypeValue = errorType;
                public final String messageValue = errorMessage;
                public final long timestamp = System.currentTimeMillis();
            };

            emitter.send(SseEmitter.event().name("error").data(eventData));

            emitter.complete();
        } catch (IOException e) {
            log.error("发送错误事件失败: {}", e.getMessage());
            emitter.completeWithError(e);
        }
    }
}
