package com.whiskerguard.ai.domain.enumeration;

/**
 * 任务优先级枚举
 */
public enum TaskPriority {
    HIGH("HIGH"),
    LOW("LOW"),
    NORMAL("NORMAL"),
    URGENT("URGENT");

    private final String value;

    TaskPriority(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
