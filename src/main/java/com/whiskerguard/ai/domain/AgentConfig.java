package com.whiskerguard.ai.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Agent配置实体
 * 存储Agent的配置参数
 */
@Entity
@Table(name = "agent_config")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AgentConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     * 为空时表示全局配置，适用于所有租户
     * Tenant ID
     * Null value indicates global configuration applicable to all tenants
     */
    @Column(name = "tenant_id", nullable = true)
    private Long tenantId;

    /**
     * 配置键
     */
    @NotNull
    @Size(max = 100)
    @Column(name = "config_key", length = 100, nullable = false)
    private String configKey;

    /**
     * 配置值
     */
    @NotNull
    @Size(max = 2000)
    @Column(name = "config_value", length = 2000, nullable = false)
    private String configValue;

    /**
     * 配置类型 (LLM, RAG, RETRY, etc.)
     * Configuration type
     */
    @Size(max = 50)
    @Column(name = "config_type", length = 50)
    private String configType;

    /**
     * 任务类型 (适用的任务类型，NULL表示适用所有类型)
     * Task type (applicable task type, NULL means applicable to all types)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "task_type")
    private com.whiskerguard.ai.domain.enumeration.AgentTaskType taskType;

    /**
     * 优先级 (数值越高优先级越高)
     * Priority (higher number means higher priority)
     */
    @Column(name = "priority")
    private Integer priority;

    /**
     * 配置描述
     */
    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 配置分组
     */
    @Size(max = 50)
    @Column(name = "config_group", length = 50)
    private String configGroup;

    /**
     * 是否启用
     */
    @NotNull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public AgentConfig id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public AgentConfig tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getConfigKey() {
        return this.configKey;
    }

    public AgentConfig configKey(String configKey) {
        this.setConfigKey(configKey);
        return this;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return this.configValue;
    }

    public AgentConfig configValue(String configValue) {
        this.setConfigValue(configValue);
        return this;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigType() {
        return this.configType;
    }

    public AgentConfig configType(String configType) {
        this.setConfigType(configType);
        return this;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public com.whiskerguard.ai.domain.enumeration.AgentTaskType getTaskType() {
        return this.taskType;
    }

    public AgentConfig taskType(com.whiskerguard.ai.domain.enumeration.AgentTaskType taskType) {
        this.setTaskType(taskType);
        return this;
    }

    public void setTaskType(com.whiskerguard.ai.domain.enumeration.AgentTaskType taskType) {
        this.taskType = taskType;
    }

    public Integer getPriority() {
        return this.priority;
    }

    public AgentConfig priority(Integer priority) {
        this.setPriority(priority);
        return this;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return this.description;
    }

    public AgentConfig description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getConfigGroup() {
        return this.configGroup;
    }

    public AgentConfig configGroup(String configGroup) {
        this.setConfigGroup(configGroup);
        return this;
    }

    public void setConfigGroup(String configGroup) {
        this.configGroup = configGroup;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public AgentConfig enabled(Boolean enabled) {
        this.setEnabled(enabled);
        return this;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public AgentConfig metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public AgentConfig version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public AgentConfig createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public AgentConfig createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public AgentConfig updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public AgentConfig updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public AgentConfig isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AgentConfig)) {
            return false;
        }
        return getId() != null && getId().equals(((AgentConfig) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AgentConfig{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", configKey='" + getConfigKey() + "'" +
            ", configValue='" + getConfigValue() + "'" +
            ", configType='" + getConfigType() + "'" +
            ", taskType='" + getTaskType() + "'" +
            ", priority=" + getPriority() +
            ", description='" + getDescription() + "'" +
            ", configGroup='" + getConfigGroup() + "'" +
            ", enabled='" + getEnabled() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
