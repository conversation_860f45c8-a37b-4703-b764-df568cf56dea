package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Agent任务上下文实体
 * Agent Task Context Entity
 *
 * 存储Agent任务执行过程中的上下文信息��中间状态
 * Store context information and intermediate states during Agent task execution
 *
 * 功能职责：
 * - 存储任务执行过程中的上下文信息
 * - 缓存中间结果数据
 * - 提供步骤间数据传递机制
 * - 记录执行历史和状态转换
 *
 * Functional responsibilities:
 * - Store context information during task execution
 * - Cache intermediate result data
 * - Provide data transfer mechanism between steps
 * - Record execution history and state transitions
 */
@Entity
@Table(name = "agent_context")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AgentContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * Primary Key ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 关联的任务ID（与AgentTask多对一关系）
     * Associated task ID (many-to-one relationship with AgentTask)
     */
    @NotNull
    @Column(name = "agent_task_id", nullable = false)
    private Long agentTaskId;

    /**
     * 上下文类型（特定业务类型可能需要不同上下文）
     * Context type (specific business types may need different contexts)
     */
    @Size(max = 50)
    @Column(name = "context_type", length = 50)
    private String contextType;

    /**
     * 上下文数据（JSON格式存储）
     * Context data (stored in JSON format)
     */
    @Lob
    @Column(name = "context_data")
    private String contextData;

    /**
     * 变量存储（键值对，JSON格式）
     * Variable storage (key-value pairs, JSON format)
     */
    @Lob
    @Column(name = "variables", columnDefinition = "TEXT")
    private String variables;

    /**
     * 临时文件引用（JSON格式）
     * Temporary file references (JSON format)
     */
    @Lob
    @Column(name = "temp_files")
    private String tempFiles;

    /**
     * 状态变更历史（JSON格式）
     * State change history (JSON format)
     */
    @Lob
    @Column(name = "history", columnDefinition = "TEXT")
    private String history;

    /**
     * 乐观锁版本
     * Optimistic lock version
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     * Creator
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     * Creation time
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     * Updater
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     * Update time
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     * Soft delete flag
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    /**
     * 关联的Agent任务（多对一关系）
     * Associated Agent task (many-to-one relationship)
     */
    @ManyToOne(optional = false)
    @NotNull
    @JoinColumn(name = "agent_task_id", insertable = false, updatable = false)
    @JsonIgnoreProperties(value = { "taskSteps", "contexts" }, allowSetters = true)
    private AgentTask agentTask;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public AgentContext id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAgentTaskId() {
        return this.agentTaskId;
    }

    public AgentContext agentTaskId(Long agentTaskId) {
        this.setAgentTaskId(agentTaskId);
        return this;
    }

    public void setAgentTaskId(Long agentTaskId) {
        this.agentTaskId = agentTaskId;
    }

    public String getContextType() {
        return this.contextType;
    }

    public AgentContext contextType(String contextType) {
        this.setContextType(contextType);
        return this;
    }

    public void setContextType(String contextType) {
        this.contextType = contextType;
    }

    public String getContextData() {
        return this.contextData;
    }

    public AgentContext contextData(String contextData) {
        this.setContextData(contextData);
        return this;
    }

    public void setContextData(String contextData) {
        this.contextData = contextData;
    }

    public String getVariables() {
        return this.variables;
    }

    public AgentContext variables(String variables) {
        this.setVariables(variables);
        return this;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public String getTempFiles() {
        return this.tempFiles;
    }

    public AgentContext tempFiles(String tempFiles) {
        this.setTempFiles(tempFiles);
        return this;
    }

    public void setTempFiles(String tempFiles) {
        this.tempFiles = tempFiles;
    }

    public String getHistory() {
        return this.history;
    }

    public AgentContext history(String history) {
        this.setHistory(history);
        return this;
    }

    public void setHistory(String history) {
        this.history = history;
    }

    public Integer getVersion() {
        return this.version;
    }

    public AgentContext version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public AgentContext createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public AgentContext createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public AgentContext updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public AgentContext updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public AgentContext isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AgentTask getAgentTask() {
        return this.agentTask;
    }

    public void setAgentTask(AgentTask agentTask) {
        this.agentTask = agentTask;
    }

    public AgentContext agentTask(AgentTask agentTask) {
        this.setAgentTask(agentTask);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AgentContext)) {
            return false;
        }
        return getId() != null && getId().equals(((AgentContext) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AgentContext{" +
            "id=" + getId() +
            ", agentTaskId=" + getAgentTaskId() +
            ", contextType='" + getContextType() + "'" +
            ", contextData='" + getContextData() + "'" +
            ", variables='" + getVariables() + "'" +
            ", tempFiles='" + getTempFiles() + "'" +
            ", history='" + getHistory() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
