package com.whiskerguard.ai.config;

import com.whiskerguard.ai.service.util.RateLimitHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 速率限制配置类
 * <p>
 * 负责配置和管理API速率限制相关的定时任务和清理工作。
 *
 * <AUTHOR> Guard AI Team
 */
@Configuration
@EnableScheduling
public class RateLimitConfig {

    private static final Logger log = LoggerFactory.getLogger(RateLimitConfig.class);

    @Autowired
    private RateLimitHandler rateLimitHandler;

    /**
     * 定期清理过期的速率限制记录
     * <p>
     * 每小时执行一次，清理超过1小时的调用记录和错误计数，
     * 避免内存泄漏和过期数据积累。
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupExpiredRateLimitRecords() {
        try {
            log.debug("开始清理过期的速率限制记录");
            rateLimitHandler.cleanupExpiredRecords();
            log.debug("速率限制记录清理完成");
        } catch (Exception e) {
            log.error("清理速率限制记录时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 定期输出速率限制统计信息（仅在DEBUG级别）
     * <p>
     * 每30分钟输出一次当前的速率限制状态，
     * 帮助监控和调试API调用情况。
     */
    @Scheduled(fixedRate = 1800000) // 每30分钟执行一次
    public void logRateLimitStatistics() {
        if (log.isDebugEnabled()) {
            log.debug("速率限制统计信息已记录");
            // 这里可以添加更详细的统计信息输出
        }
    }
}
