package com.whiskerguard.ai.service.util;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

/**
 * API速率限制处理工具类
 * <p>
 * 用于处理各种AI服务的速率限制问题，包括：
 * 1. 429错误的智能重试策略
 * 2. 动态退避算法
 * 3. 请求频率控制
 * 4. 错误分析和建议
 *
 * <AUTHOR> Guard AI Team
 */
@Component
public class RateLimitHandler {

    private static final Logger log = LoggerFactory.getLogger(RateLimitHandler.class);

    // 存储每个API服务的最后调用时间
    private final ConcurrentHashMap<String, LocalDateTime> lastCallTimes = new ConcurrentHashMap<>();

    // 存储每个API服务的连续429错误次数
    private final ConcurrentHashMap<String, Integer> consecutiveRateLimitErrors = new ConcurrentHashMap<>();

    /**
     * 检查是否为速率限制错误
     *
     * @param exception 异常对象
     * @return 是否为速率限制错误
     */
    public boolean isRateLimitError(Exception exception) {
        if (exception == null) {
            return false;
        }

        String message = exception.getMessage();
        if (message == null) {
            return false;
        }

        // 检查常见的速率限制错误标识
        return (
            message.contains("429") ||
            message.contains("Too Many Requests") ||
            message.contains("Rate limit") ||
            message.contains("rate_limit_exceeded") ||
            message.contains("请求过于频繁") ||
            (exception instanceof WebClientResponseException.TooManyRequests)
        );
    }

    /**
     * 计算智能退避时间
     *
     * @param apiKey API服务标识（如 "kimi", "doubao" 等）
     * @param attemptNumber 当前重试次数（从1开始）
     * @return 建议的等待时间（毫秒）
     */
    public long calculateBackoffTime(String apiKey, int attemptNumber) {
        // 获取连续错误次数
        int consecutiveErrors = consecutiveRateLimitErrors.getOrDefault(apiKey, 0);

        // 基础退避时间（毫秒）
        long baseBackoff = getBaseBackoffTime(apiKey);

        // 指数退避 + 随机抖动
        double multiplier = Math.pow(2.0, attemptNumber - 1);
        long calculatedBackoff = (long) (baseBackoff * multiplier);

        // 根据连续错误次数增加额外延迟
        if (consecutiveErrors > 0) {
            calculatedBackoff += consecutiveErrors * 2000; // 每次连续错误增加2秒
        }

        // 添加随机抖动（±20%）避免雷群效应
        double jitterFactor = 0.8 + (ThreadLocalRandom.current().nextDouble() * 0.4);
        calculatedBackoff = (long) (calculatedBackoff * jitterFactor);

        // 设置最大退避时间（60秒）
        long maxBackoff = 60000;
        calculatedBackoff = Math.min(calculatedBackoff, maxBackoff);

        log.debug(
            "计算退避时间 - API: {}, 尝试次数: {}, 连续错误: {}, 退避时间: {}ms",
            apiKey,
            attemptNumber,
            consecutiveErrors,
            calculatedBackoff
        );

        return calculatedBackoff;
    }

    /**
     * 记录速率限制错误
     *
     * @param apiKey API服务标识
     */
    public void recordRateLimitError(String apiKey) {
        consecutiveRateLimitErrors.merge(apiKey, 1, Integer::sum);
        log.warn("记录速率限制错误 - API: {}, 连续错误次数: {}", apiKey, consecutiveRateLimitErrors.get(apiKey));
    }

    /**
     * 记录成功调用（重置错误计数）
     *
     * @param apiKey API服务标识
     */
    public void recordSuccessfulCall(String apiKey) {
        lastCallTimes.put(apiKey, LocalDateTime.now());
        consecutiveRateLimitErrors.remove(apiKey);
        log.debug("记录成功调用 - API: {}, 重置错误计数", apiKey);
    }

    /**
     * 检查是否应该延迟调用
     *
     * @param apiKey API服务标识
     * @return 建议延迟的毫秒数，0表示可以立即调用
     */
    public long shouldDelayCall(String apiKey) {
        LocalDateTime lastCall = lastCallTimes.get(apiKey);
        if (lastCall == null) {
            return 0;
        }

        long minInterval = getMinCallInterval(apiKey);
        long timeSinceLastCall = Duration.between(lastCall, LocalDateTime.now()).toMillis();

        if (timeSinceLastCall < minInterval) {
            long delay = minInterval - timeSinceLastCall;
            log.debug("建议延迟调用 - API: {}, 延迟时间: {}ms", apiKey, delay);
            return delay;
        }

        return 0;
    }

    /**
     * 获取错误建议信息
     *
     * @param apiKey API服务标识
     * @param exception 异常对象
     * @return 错误建议信息
     */
    public String getErrorAdvice(String apiKey, Exception exception) {
        StringBuilder advice = new StringBuilder();

        if (isRateLimitError(exception)) {
            int consecutiveErrors = consecutiveRateLimitErrors.getOrDefault(apiKey, 0);

            advice.append("检测到API速率限制错误 (429 Too Many Requests)\n");
            advice.append("建议措施：\n");
            advice.append("1. 系统将自动进行智能重试，请稍候...\n");
            advice.append("2. 当前连续错误次数: ").append(consecutiveErrors).append("\n");

            if (consecutiveErrors > 3) {
                advice.append("3. 连续错误较多，建议检查API配额或联系服务提供商\n");
            } else {
                advice.append("3. 建议降低调用频率或稍后重试\n");
            }

            advice.append("4. 如问题持续，请检查API Key配额状态\n");
        }

        return advice.toString();
    }

    /**
     * 获取基础退避时间
     *
     * @param apiKey API服务标识
     * @return 基础退避时间（毫秒）
     */
    private long getBaseBackoffTime(String apiKey) {
        switch (apiKey.toLowerCase()) {
            case "kimi":
                return 5000; // Kimi API 基础退避5秒
            case "doubao":
                return 3000; // 豆包 API 基础退避3秒
            case "claude":
                return 3000; // Claude API 基础退避3秒
            case "deepseek":
                return 3000; // DeepSeek API 基础退避3秒
            case "qwen":
                return 3000; // 通义千问 API 基础退避3秒
            default:
                return 2000; // 默认基础退避2秒
        }
    }

    /**
     * 获取最小调用间隔
     *
     * @param apiKey API服务标识
     * @return 最小调用间隔（毫秒）
     */
    private long getMinCallInterval(String apiKey) {
        switch (apiKey.toLowerCase()) {
            case "kimi":
                return 1000; // Kimi API 最小间隔1秒
            case "doubao":
                return 500; // 豆包 API 最小间隔0.5秒
            case "claude":
                return 500; // Claude API 最小间隔0.5秒
            case "deepseek":
                return 500; // DeepSeek API 最小间隔0.5秒
            case "qwen":
                return 500; // 通义千问 API 最小间隔0.5秒
            default:
                return 300; // 默认最小间隔0.3秒
        }
    }

    /**
     * 清理过期的错误记录（定期清理任务可调用）
     */
    public void cleanupExpiredRecords() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(1);
        lastCallTimes.entrySet().removeIf(entry -> entry.getValue().isBefore(cutoff));
        log.debug("清理过期的调用记录，保留最近1小时的记录");
    }
}
