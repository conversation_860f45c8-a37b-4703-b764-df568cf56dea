package com.whiskerguard.ai.service.agent.core.processor.impl;

import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService;
import com.whiskerguard.ai.service.agent.core.processor.StepProcessor;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * LLM步骤处理器
 * LLM Step Processor
 *
 * 处理大语言模型调用相关的步骤
 * Process Large Language Model invocation related steps
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class LlmStepProcessor implements StepProcessor {

    private static final Logger log = LoggerFactory.getLogger(LlmStepProcessor.class);

    private final LlmOrchestrationService llmService;

    public LlmStepProcessor(LlmOrchestrationService llmService) {
        this.llmService = llmService;
    }

    @Override
    public String getType() {
        return "LLM";
    }

    @Override
    public String process(String input, Map<String, Object> parameters) {
        log.debug("开始处理LLM步骤，输入长度: {}, 参数: {}", input != null ? input.length() : 0, parameters);

        try {
            // 解析参数
            Long tenantId = (Long) parameters.get("tenantId");
            String toolKey = (String) parameters.getOrDefault("toolKey", "kimi");

            @SuppressWarnings("unchecked")
            Map<String, Object> metadata = (Map<String, Object>) parameters.getOrDefault("metadata", new HashMap<>());

            // 构建提示词
            String prompt = buildPrompt(input, parameters);

            // 调用LLM服务
            String result = llmService.invokeLlm(tenantId, toolKey, prompt, metadata);

            log.debug("LLM步骤处理完成，结果长度: {}", result != null ? result.length() : 0);
            return result;
        } catch (Exception e) {
            log.error("LLM步骤处理失败", e);
            throw new RuntimeException("LLM步骤处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateParameters(Map<String, Object> parameters) {
        // LLM步骤的参数都是可选的，使用默认值
        return true;
    }

    @Override
    public String getDescription() {
        return "大语言模型步骤处理器，支持多种LLM模型的调用";
    }

    /**
     * 构建提示词
     * Build prompt
     *
     * @param input 输入数据 / Input data
     * @param parameters 步骤参数 / Step parameters
     * @return 构建的提示词 / Built prompt
     */
    private String buildPrompt(String input, Map<String, Object> parameters) {
        String promptTemplate = (String) parameters.get("promptTemplate");

        if (promptTemplate != null && !promptTemplate.trim().isEmpty()) {
            // 使用自定义提示词模板
            return String.format(promptTemplate, input);
        } else {
            // 使用默认提示词
            return input;
        }
    }
}
