package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务编排服务接口
 * Task Orchestrator Service Interface
 *
 * 提供Agent任务工作流的编排和执行能力
 * Provide workflow orchestration and execution capabilities for Agent tasks
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface TaskOrchestratorServiceInterface {
    /**
     * 执行工作流
     * Execute workflow
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @param steps 步骤定义列表 / Step definition list
     */
    void executeWorkflow(Long agentTaskId, List<StepDefinition> steps);

    /**
     * 异步执行工作流
     * Execute workflow asynchronously
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @param steps 步骤定义列表 / Step definition list
     * @return CompletableFuture对象 / CompletableFuture object
     */
    CompletableFuture<Void> executeWorkflowAsync(Long agentTaskId, List<StepDefinition> steps);

    /**
     * 并行执行工作流步骤
     * Execute workflow steps in parallel
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @param parallelSteps 并行执行的步骤列表 / List of steps to execute in parallel
     */
    void executeWorkflowParallel(Long agentTaskId, List<StepDefinition> parallelSteps);

    /**
     * 执行单个步骤
     * Execute single step
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @param stepDefinition 步骤定义 / Step definition
     * @return 步骤输出结果 / Step output result
     */
    String executeStep(Long agentTaskId, StepDefinition stepDefinition);

    /**
     * 重试失败的步骤
     * Retry failed step
     *
     * @param stepId 步骤ID / Step ID
     * @return 是否重试成功 / Whether retry was successful
     */
    boolean retryStep(Long stepId);

    /**
     * 取消工作流执行
     * Cancel workflow execution
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @return 是否取消成功 / Whether cancellation was successful
     */
    boolean cancelWorkflow(Long agentTaskId);

    /**
     * 获取工作流执行状态
     * Get workflow execution status
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @return 工作流状态信息 / Workflow status information
     */
    WorkflowStatus getWorkflowStatus(Long agentTaskId);

    /**
     * 恢复中断的工作流
     * Resume interrupted workflow
     *
     * @param agentTaskId 代理任务ID / Agent task ID
     * @return 是否恢复成功 / Whether resume was successful
     */
    boolean resumeWorkflow(Long agentTaskId);

    /**
     * 工作流状态信息
     * Workflow status information
     */
    class WorkflowStatus {

        private final boolean isRunning;
        private final boolean isCompleted;
        private final boolean isFailed;
        private final int totalSteps;
        private final int completedSteps;
        private final int failedSteps;
        private final String lastError;

        public WorkflowStatus(
            boolean isRunning,
            boolean isCompleted,
            boolean isFailed,
            int totalSteps,
            int completedSteps,
            int failedSteps,
            String lastError
        ) {
            this.isRunning = isRunning;
            this.isCompleted = isCompleted;
            this.isFailed = isFailed;
            this.totalSteps = totalSteps;
            this.completedSteps = completedSteps;
            this.failedSteps = failedSteps;
            this.lastError = lastError;
        }

        // Getters
        public boolean isRunning() {
            return isRunning;
        }

        public boolean isCompleted() {
            return isCompleted;
        }

        public boolean isFailed() {
            return isFailed;
        }

        public int getTotalSteps() {
            return totalSteps;
        }

        public int getCompletedSteps() {
            return completedSteps;
        }

        public int getFailedSteps() {
            return failedSteps;
        }

        public String getLastError() {
            return lastError;
        }
    }
}
