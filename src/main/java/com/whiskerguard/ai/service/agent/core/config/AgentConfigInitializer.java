package com.whiskerguard.ai.service.agent.core.config;

import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.service.agent.core.AgentConfigCoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * Agent配置初始化器
 * Agent Configuration Initializer
 *
 * 在应用启动时初始化默认配置
 * Initialize default configurations when application starts
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class AgentConfigInitializer implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(AgentConfigInitializer.class);

    private final AgentConfigCoreService agentConfigService;

    public AgentConfigInitializer(AgentConfigCoreService agentConfigService) {
        this.agentConfigService = agentConfigService;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化Agent默认配置 / Starting to initialize Agent default configurations");

        try {
            // 初始化LLM相关配置
            initializeLlmConfigs();

            // 初始化重试相关配置
            initializeRetryConfigs();

            // 初始化RAG相关配置
            initializeRagConfigs();

            // 初始化任务类型特定配置
            initializeTaskTypeConfigs();

            log.info("Agent默认配置初始化完成 / Agent default configurations initialization completed");
        } catch (Exception e) {
            log.error("Agent配置初始化失败 / Agent configuration initialization failed", e);
            throw e;
        }
    }

    /**
     * 初始化LLM相关配置
     * Initialize LLM related configurations
     */
    private void initializeLlmConfigs() {
        log.debug("初始化LLM配置 / Initializing LLM configurations");

        // 默认LLM模型
        setDefaultConfigIfNotExists("llm.default.model", "kimi", null, null, "LLM", "llm", "默认LLM模型", 100);

        // 默认温度
        setDefaultConfigIfNotExists("llm.default.temperature", "0.7", null, null, "LLM", "llm", "默认LLM温度参数", 100);

        // 默认最大令牌数
        setDefaultConfigIfNotExists("llm.default.max_tokens", "4000", null, null, "LLM", "llm", "默认最大令牌数", 100);

        // 默认超时时间（秒）
        setDefaultConfigIfNotExists("llm.default.timeout", "60", null, null, "LLM", "llm", "默认LLM调用超时时间（秒）", 100);
    }

    /**
     * 初始化重试相关配置
     * Initialize retry related configurations
     */
    private void initializeRetryConfigs() {
        log.debug("初始化重试配置 / Initializing retry configurations");

        // 默认最大重试次数
        setDefaultConfigIfNotExists("retry.max", "3", null, null, "RETRY", "retry", "默认最大重试次数", 100);

        // 默认重试延迟（毫秒）
        setDefaultConfigIfNotExists("retry.backoff.ms", "1000", null, null, "RETRY", "retry", "默认重试延迟时间（毫秒）", 100);

        // 是否启用指数退避
        setDefaultConfigIfNotExists("retry.exponential_backoff", "true", null, null, "RETRY", "retry", "是否启用指数退避策略", 100);

        // 最大退避时间（毫秒）
        setDefaultConfigIfNotExists("retry.max_backoff.ms", "30000", null, null, "RETRY", "retry", "最大退避时间（毫秒）", 100);
    }

    /**
     * 初始化RAG相关配置
     * Initialize RAG related configurations
     */
    private void initializeRagConfigs() {
        log.debug("初始化RAG配置 / Initializing RAG configurations");

        // 默认检索结果数量
        setDefaultConfigIfNotExists("rag.default.max_results", "10", null, null, "RAG", "rag", "默认检索结果数量", 100);

        // 默认相似度阈值
        setDefaultConfigIfNotExists("rag.default.min_score", "0.7", null, null, "RAG", "rag", "默认相似度阈值", 100);

        // 是否启用缓存
        setDefaultConfigIfNotExists("rag.cache.enabled", "true", null, null, "RAG", "rag", "是否启用RAG结果缓存", 100);

        // 缓存过期时间（小时）
        setDefaultConfigIfNotExists("rag.cache.expire_hours", "24", null, null, "RAG", "rag", "RAG缓存过期时间（小时）", 100);
    }

    /**
     * 初始化任务类型特定配置
     * Initialize task type specific configurations
     */
    private void initializeTaskTypeConfigs() {
        log.debug("初始化任务类型特定配置 / Initializing task type specific configurations");

        // 合同审查特定配置
        initializeContractReviewConfigs();

        // 外规内化特定配置
        initializeRegulationInternalizationConfigs();

        // 制度审查特定配置
        initializePolicyReviewConfigs();
    }

    /**
     * 初始化合同审查配置
     * Initialize contract review configurations
     */
    private void initializeContractReviewConfigs() {
        // 合同审查特定的重试次数
        setDefaultConfigIfNotExists(
            "retry.max",
            "5",
            null,
            AgentTaskType.CONTRACT_REVIEW,
            "RETRY",
            "retry",
            "合同审查任务最大重试次数",
            200
        );

        // 合同审查特定的LLM模型
        setDefaultConfigIfNotExists(
            "llm.preferred.model",
            "gpt-4",
            null,
            AgentTaskType.CONTRACT_REVIEW,
            "LLM",
            "llm",
            "合同审查首选LLM模型",
            200
        );

        // 并行处理步骤数
        setDefaultConfigIfNotExists(
            "workflow.parallel.steps",
            "3",
            null,
            AgentTaskType.CONTRACT_REVIEW,
            "WORKFLOW",
            "workflow",
            "合同审查并行处理步骤数",
            200
        );
    }

    /**
     * 初始化外规内化配置
     * Initialize regulation internalization configurations
     */
    private void initializeRegulationInternalizationConfigs() {
        // 外规内化特定的重试次数
        setDefaultConfigIfNotExists(
            "retry.max",
            "4",
            null,
            AgentTaskType.REGULATION_INTERNALIZATION,
            "RETRY",
            "retry",
            "外规内化任务最大重试次数",
            200
        );

        // 检索结果数量
        setDefaultConfigIfNotExists(
            "rag.max_results",
            "15",
            null,
            AgentTaskType.REGULATION_INTERNALIZATION,
            "RAG",
            "rag",
            "外规内化检索结果数量",
            200
        );
    }

    /**
     * 初始化制度审查配置
     * Initialize policy review configurations
     */
    private void initializePolicyReviewConfigs() {
        // 制度审查特定的重试次数
        setDefaultConfigIfNotExists("retry.max", "3", null, AgentTaskType.POLICY_REVIEW, "RETRY", "retry", "制度审查任务最大重试次数", 200);

        // 风险评级阈值
        setDefaultConfigIfNotExists(
            "analysis.risk_threshold",
            "0.6",
            null,
            AgentTaskType.POLICY_REVIEW,
            "ANALYSIS",
            "analysis",
            "制度审查风险评级阈值",
            200
        );
    }

    /**
     * 设置默认配置（如果不存在）
     * Set default configuration if not exists
     */
    private void setDefaultConfigIfNotExists(
        String key,
        String value,
        Long tenantId,
        AgentTaskType taskType,
        String configType,
        String configGroup,
        String description,
        Integer priority
    ) {
        if (!agentConfigService.hasConfig(key, tenantId, taskType)) {
            agentConfigService.setConfig(key, value, tenantId, taskType, configType, configGroup, description, priority);
            log.debug(
                "设置默认配置 - 键: {}, 值: {}, 租户: {}, 任务类型: {} / Set default config - Key: {}, Value: {}, Tenant: {}, Task type: {}",
                key,
                value,
                tenantId,
                taskType,
                key,
                value,
                tenantId,
                taskType
            );
        }
    }
}
