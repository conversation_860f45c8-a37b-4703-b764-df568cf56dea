/**
 * =============================================================================
 * 合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：LlmOrchestrationService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：LLM编排服务 - LLM Orchestration Service
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.invocation.ResultIntegrationEngine;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * LLM编排服务
 * LLM Orchestration Service
 *
 * 负责整合多个LLM服务的调用、结果合并和配置管理
 * Responsible for integrating multiple LLM service calls, result merging and configuration management
 */
@Service
public class LlmOrchestrationService {

    private static final Logger log = LoggerFactory.getLogger(LlmOrchestrationService.class);

    private final AiInvocationService aiInvocationService;
    private final ResultIntegrationEngine resultIntegrationEngine;
    private final AgentConfigCoreService agentConfigService;

    /**
     * 构造函数注入依赖
     * Constructor with dependency injection
     */
    public LlmOrchestrationService(
        AiInvocationService aiInvocationService,
        ResultIntegrationEngine resultIntegrationEngine,
        AgentConfigCoreService agentConfigService
    ) {
        this.aiInvocationService = aiInvocationService;
        this.resultIntegrationEngine = resultIntegrationEngine;
        this.agentConfigService = agentConfigService;
    }

    /**
     * 执行LLM调用并集成结果
     * Execute LLM invocation and integrate results
     *
     * @param prompt 提示词 Prompt text
     * @param taskType 任务类型 Task type
     * @param tenantId 租户ID Tenant ID
     * @param contextParams 上下文参数 Context parameters
     * @return 集成后的结果 Integrated result
     */
    public String orchestrateLlmCall(String prompt, AgentTaskType taskType, Long tenantId, Map<String, Object> contextParams) {
        try {
            log.debug("开始LLM编排调用 - Starting LLM orchestration call for taskType: {}, tenantId: {}", taskType, tenantId);

            // 1. 获取配置驱动的LLM参数
            // Get configuration-driven LLM parameters
            Map<String, Object> llmParams = getLlmParameters(taskType, tenantId);

            // 2. 执行LLM调用
            // Execute LLM invocation
            String rawResult = performLlmInvocation(prompt, llmParams, contextParams);

            // 3. 结果集成处理
            // Result integration processing
            String integratedResult = integrateResult(rawResult, taskType, contextParams);

            log.debug("LLM编排调用完成 - LLM orchestration call completed successfully");
            return integratedResult;
        } catch (Exception e) {
            log.error("LLM编排调用失败 - LLM orchestration call failed for taskType: {}, tenantId: {}", taskType, tenantId, e);
            throw new RuntimeException("LLM编排调用失败 - LLM orchestration call failed", e);
        }
    }

    /**
     * 获取配置驱动的LLM参数
     * Get configuration-driven LLM parameters
     */
    private Map<String, Object> getLlmParameters(AgentTaskType taskType, Long tenantId) {
        try {
            // 使用AgentConfigCoreService获取配置参数
            // Use AgentConfigCoreService to get configuration parameters
            Map<String, Object> params = new java.util.HashMap<>();

            // 获取模型配置
            // Get model configuration
            String model = agentConfigService.getConfig("llm.model", "qwen-plus", tenantId, taskType);
            params.put("model", model);

            // 获取温度参数
            // Get temperature parameter
            Double temperature = agentConfigService.getConfig("llm.temperature", 0.7, tenantId, taskType);
            params.put("temperature", temperature);

            // 获取最大token数
            // Get max tokens
            Integer maxTokens = agentConfigService.getConfig("llm.max_tokens", 2000, tenantId, taskType);
            params.put("max_tokens", maxTokens);

            // 获取重试次数
            // Get retry count
            Integer retryCount = agentConfigService.getConfig("llm.retry_count", 3, tenantId, taskType);
            params.put("retry_count", retryCount);

            log.debug("获取LLM配置参数 - Retrieved LLM configuration parameters: {}", params);
            return params;
        } catch (Exception e) {
            log.warn("获取LLM配置参数失败，使用默认参数 - Failed to get LLM configuration parameters, using defaults", e);
            // 返回默认参数
            // Return default parameters
            Map<String, Object> defaultParams = new java.util.HashMap<>();
            defaultParams.put("model", "qwen-plus");
            defaultParams.put("temperature", 0.7);
            defaultParams.put("max_tokens", 2000);
            defaultParams.put("retry_count", 3);
            return defaultParams;
        }
    }

    /**
     * 执行LLM调用
     * Perform LLM invocation
     */
    private String performLlmInvocation(String prompt, Map<String, Object> llmParams, Map<String, Object> contextParams) {
        try {
            // 合并参数
            // Merge parameters
            Map<String, Object> allParams = new java.util.HashMap<>(llmParams);
            if (contextParams != null) {
                allParams.putAll(contextParams);
            }

            // 调用AI服务
            // Call AI service
            String model = (String) llmParams.get("model");
            log.debug("执行LLM调用 - Performing LLM invocation with model: {}", model);

            // 构建AiInvocationRequestDTO
            // Build AiInvocationRequestDTO
            com.whiskerguard.ai.service.dto.AiInvocationRequestDTO requestDto = new com.whiskerguard.ai.service.dto.AiInvocationRequestDTO(
                model,
                prompt,
                allParams,
                contextParams != null ? (Long) contextParams.get("tenantId") : null,
                contextParams != null ? (Long) contextParams.get("employeeId") : null,
                null, // templateKey
                null, // templateType
                null, // templateVariables
                false // useTemplate
            );

            // 调用AI服务并获取结果
            // Call AI service and get result
            com.whiskerguard.ai.service.dto.AiRequestDTO result = aiInvocationService.invoke(requestDto);
            return result.getResponse();
        } catch (Exception e) {
            log.error("LLM调用执行失败 - LLM invocation execution failed", e);
            throw new RuntimeException("LLM调用执行失败 - LLM invocation execution failed", e);
        }
    }

    /**
     * 集成处理结果
     * Integrate processing results
     */
    private String integrateResult(String rawResult, AgentTaskType taskType, Map<String, Object> contextParams) {
        try {
            log.debug("开始结果集成处理 - Starting result integration processing for taskType: {}", taskType);

            // 使用ResultIntegrationEngine进行结果集成
            // Use ResultIntegrationEngine for result integration
            String integratedResult = resultIntegrationEngine.integrateResult(rawResult, taskType.name(), contextParams);

            log.debug("结果集成处理完成 - Result integration processing completed");
            return integratedResult;
        } catch (Exception e) {
            log.warn("结果集成处理失败，返回原始结果 - Result integration processing failed, returning raw result", e);
            return rawResult;
        }
    }

    /**
     * 批量LLM调用编排
     * Batch LLM invocation orchestration
     *
     * @param prompts 提示词列表 List of prompts
     * @param taskType 任务类型 Task type
     * @param tenantId 租户ID Tenant ID
     * @param contextParams 上下文参数 Context parameters
     * @return 批量结果列表 List of batch results
     */
    public java.util.List<String> orchestrateBatchLlmCall(
        java.util.List<String> prompts,
        AgentTaskType taskType,
        Long tenantId,
        Map<String, Object> contextParams
    ) {
        try {
            log.debug("开始批量LLM编排调用 - Starting batch LLM orchestration call for {} prompts", prompts.size());

            java.util.List<String> results = new java.util.ArrayList<>();
            Map<String, Object> llmParams = getLlmParameters(taskType, tenantId);

            for (String prompt : prompts) {
                try {
                    String rawResult = performLlmInvocation(prompt, llmParams, contextParams);
                    String integratedResult = integrateResult(rawResult, taskType, contextParams);
                    results.add(integratedResult);
                } catch (Exception e) {
                    log.error("批量调用中单个prompt处理失败 - Single prompt processing failed in batch call", e);
                    results.add("处理失败 - Processing failed: " + e.getMessage());
                }
            }

            log.debug("批量LLM编排调用完成 - Batch LLM orchestration call completed, {} results", results.size());
            return results;
        } catch (Exception e) {
            log.error("批量LLM编排调用失败 - Batch LLM orchestration call failed", e);
            throw new RuntimeException("批量LLM编排调用失败 - Batch LLM orchestration call failed", e);
        }
    }

    /**
     * 异步LLM调用编排
     * Asynchronous LLM invocation orchestration
     *
     * @param prompt 提示词 Prompt text
     * @param taskType 任务类型 Task type
     * @param tenantId 租户ID Tenant ID
     * @param contextParams 上下文参数 Context parameters
     * @return CompletableFuture异步结果 CompletableFuture async result
     */
    public java.util.concurrent.CompletableFuture<String> orchestrateAsyncLlmCall(
        String prompt,
        AgentTaskType taskType,
        Long tenantId,
        Map<String, Object> contextParams
    ) {
        return java.util.concurrent.CompletableFuture.supplyAsync(() -> {
            try {
                return orchestrateLlmCall(prompt, taskType, tenantId, contextParams);
            } catch (Exception e) {
                log.error("异步LLM编排调用失败 - Async LLM orchestration call failed", e);
                throw new RuntimeException("异步LLM编排调用失败 - Async LLM orchestration call failed", e);
            }
        });
    }

    /**
     * 简单LLM调用接口 - 为LlmStepProcessor提供
     * Simple LLM invocation interface - for LlmStepProcessor
     *
     * @param tenantId 租户ID Tenant ID
     * @param prompt 提示词 Prompt
     * @param model 模型名称 Model name
     * @param contextParams 上下文参数 Context parameters
     * @return LLM响应 LLM response
     */
    public String invokeLlm(Long tenantId, String prompt, String model, Map<String, Object> contextParams) {
        try {
            log.debug("简单LLM调用 - Simple LLM invocation for tenantId: {}, model: {}", tenantId, model);

            // 构建上下文参数
            Map<String, Object> mergedParams = new java.util.HashMap<>();
            if (contextParams != null) {
                mergedParams.putAll(contextParams);
            }
            mergedParams.put("tenantId", tenantId);
            mergedParams.put("employeeId", 1L); // 默认员工ID，实际使用时应从上下文获取

            // 调用核心方法
            return orchestrateLlmCall(prompt, AgentTaskType.CONTRACT_REVIEW, tenantId, mergedParams);
        } catch (Exception e) {
            log.error("简单LLM调用失败 - Simple LLM invocation failed", e);
            throw new RuntimeException("LLM调用失败 - LLM invocation failed", e);
        }
    }

    /**
     * 带重试的LLM调用 - 为业务Agent服务提供
     * LLM invocation with retry - for business Agent services
     */
    public String invokeLlmWithRetry(Long tenantId, String prompt, String model, Map<String, Long> metadata, int maxRetries) {
        try {
            log.debug("带重试的LLM调用 - LLM invocation with retry for tenantId: {}, maxRetries: {}", tenantId, maxRetries);

            Map<String, Object> contextParams = new java.util.HashMap<>();
            contextParams.put("tenantId", tenantId);
            contextParams.put("employeeId", 1L);
            contextParams.put("maxRetries", maxRetries);
            if (metadata != null) {
                contextParams.putAll(metadata);
            }

            // 调用核心方法，重试逻辑可在此处实现
            return orchestrateLlmCall(prompt, AgentTaskType.CONTRACT_REVIEW, tenantId, contextParams);
        } catch (Exception e) {
            log.error("带重试的LLM调用失败 - LLM invocation with retry failed", e);
            throw new RuntimeException("LLM调用失败 - LLM invocation failed", e);
        }
    }

    /**
     * 批量LLM调用 - 为业务Agent服务提供
     * Batch LLM invocation - for business Agent services
     */
    public java.util.List<String> batchInvokeLlm(Long tenantId, java.util.List<LlmRequest> requests) {
        try {
            log.debug("批量LLM调用 - Batch LLM invocation for tenantId: {}, requests count: {}", tenantId, requests.size());

            java.util.List<String> results = new java.util.ArrayList<>();

            for (LlmRequest request : requests) {
                try {
                    Map<String, Object> contextParams = new java.util.HashMap<>();
                    contextParams.put("tenantId", tenantId);
                    contextParams.put("employeeId", 1L);
                    if (request.getMetadata() != null) {
                        contextParams.putAll(request.getMetadata());
                    }

                    String result = orchestrateLlmCall(request.getPrompt(), AgentTaskType.CONTRACT_REVIEW, tenantId, contextParams);
                    results.add(result);
                } catch (Exception e) {
                    log.error("批量调用中单个请求失败 - Single request failed in batch invocation", e);
                    results.add("处理失败 - Processing failed: " + e.getMessage());
                }
            }

            log.debug("批量LLM调用完成 - Batch LLM invocation completed, {} results", results.size());
            return results;
        } catch (Exception e) {
            log.error("批量LLM调用失败 - Batch LLM invocation failed", e);
            throw new RuntimeException("批量LLM调用失败 - Batch LLM invocation failed", e);
        }
    }

    /**
     * LLM请求对象
     * LLM Request Object
     */
    public static class LlmRequest {

        private String prompt;
        private String model;
        private Double temperature;
        private Integer maxTokens;
        private Map<String, Object> metadata;

        public LlmRequest() {}

        public LlmRequest(String prompt) {
            this.prompt = prompt;
        }

        public LlmRequest(String prompt, String model) {
            this.prompt = prompt;
            this.model = model;
        }

        public LlmRequest(String prompt, String model, Map<String, Object> metadata) {
            this.prompt = prompt;
            this.model = model;
            this.metadata = metadata;
        }

        // Getters and Setters
        public String getPrompt() {
            return prompt;
        }

        public void setPrompt(String prompt) {
            this.prompt = prompt;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public Integer getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }

    /**
     * 链式LLM请求对象
     * Chain LLM Request Object
     */
    public static class ChainLlmRequest {

        private java.util.List<LlmRequest> requests;
        private ChainStrategy strategy;

        public ChainLlmRequest() {
            this.requests = new java.util.ArrayList<>();
        }

        public ChainLlmRequest(java.util.List<LlmRequest> requests) {
            this.requests = requests;
        }

        public ChainLlmRequest(java.util.List<LlmRequest> requests, ChainStrategy strategy) {
            this.requests = requests;
            this.strategy = strategy;
        }

        // Getters and Setters
        public java.util.List<LlmRequest> getRequests() {
            return requests;
        }

        public void setRequests(java.util.List<LlmRequest> requests) {
            this.requests = requests;
        }

        public ChainStrategy getStrategy() {
            return strategy;
        }

        public void setStrategy(ChainStrategy strategy) {
            this.strategy = strategy;
        }

        public ChainLlmRequest addRequest(LlmRequest request) {
            this.requests.add(request);
            return this;
        }
    }

    /**
     * 链式执行策略
     * Chain Execution Strategy
     */
    public enum ChainStrategy {
        SEQUENTIAL, // 顺序执行
        PARALLEL, // 并行执行
        CONDITIONAL, // 条件执行
    }
}
