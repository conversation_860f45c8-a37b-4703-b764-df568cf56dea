package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import java.util.List;

/**
 * 任务步骤核心服务接口
 * Task Step Core Service Interface
 *
 * 提供Agent任务步骤的管理和执行功能
 * Provide management and execution functions for Agent task steps
 *
 * 主要功能：
 * - 创建和管理任务步骤
 * - 记录步骤执行状态和结果
 * - 支持步骤重试和恢复
 * - 提供步骤依赖关系管理
 *
 * Main functions:
 * - Create and manage task steps
 * - Record step execution status and results
 * - Support step retry and recovery
 * - Provide step dependency management
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface TaskStepCoreService {
    /**
     * 创建新的任务步骤
     * Create a new task step
     *
     * @param agentTaskId 关联的任务ID / Associated task ID
     * @param stepName 步骤名称 / Step name
     * @param stepType 步骤类型 / Step type
     * @param stepOrder 执行顺序 / Execution order
     * @return 创建的步骤实体 / Created step entity
     */
    TaskStep createStep(Long agentTaskId, String stepName, String stepType, Integer stepOrder);

    /**
     * 创建新的任务步骤（带描述）
     * Create a new task step (with description)
     *
     * @param agentTaskId 关联的任务ID / Associated task ID
     * @param stepName 步骤名称 / Step name
     * @param stepDescription 步骤描述 / Step description
     * @param stepType 步骤类型 / Step type
     * @param stepOrder 执行顺序 / Execution order
     * @return 创建的步骤实体 / Created step entity
     */
    TaskStep createStep(Long agentTaskId, String stepName, String stepDescription, String stepType, Integer stepOrder);

    /**
     * 更新步骤状态
     * Update step status
     *
     * @param stepId 步骤ID / Step ID
     * @param status 新状态 / New status
     */
    void updateStepStatus(Long stepId, TaskStepStatus status);

    /**
     * 记录步骤输入数据
     * Record step input data
     *
     * @param stepId 步骤ID / Step ID
     * @param inputData 输入数据 / Input data
     */
    void recordInput(Long stepId, String inputData);

    /**
     * 记录步骤输出数据
     * Record step output data
     *
     * @param stepId 步骤ID / Step ID
     * @param outputData 输出数据 / Output data
     */
    void recordOutput(Long stepId, String outputData);

    /**
     * 记录步骤错误信息
     * Record step error message
     *
     * @param stepId 步骤ID / Step ID
     * @param errorMessage 错误信息 / Error message
     */
    void recordError(Long stepId, String errorMessage);

    /**
     * 开始步骤执行
     * Start step execution
     *
     * @param stepId 步骤ID / Step ID
     */
    void startStep(Long stepId);

    /**
     * 完成步骤执行（成功）
     * Complete step execution (success)
     *
     * @param stepId 步骤ID / Step ID
     * @param outputData 输出数据 / Output data
     */
    void completeStep(Long stepId, String outputData);

    /**
     * 失败步骤执行
     * Fail step execution
     *
     * @param stepId 步骤ID / Step ID
     * @param errorMessage 错误信息 / Error message
     */
    void failStep(Long stepId, String errorMessage);

    /**
     * 跳过步骤执行
     * Skip step execution
     *
     * @param stepId 步骤ID / Step ID
     * @param reason 跳过原因 / Skip reason
     */
    void skipStep(Long stepId, String reason);

    /**
     * 重试步骤执行
     * Retry step execution
     *
     * @param stepId 步骤ID / Step ID
     * @return 是否可以重试 / Whether can retry
     */
    boolean retryStep(Long stepId);

    /**
     * 获取任务的所有步骤
     * Get all steps of a task
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 步骤列表 / List of steps
     */
    List<TaskStep> getTaskSteps(Long agentTaskId);

    /**
     * 获取单个步骤
     * Get single step
     *
     * @param stepId 步骤ID / Step ID
     * @return 步骤实体 / Step entity
     */
    TaskStep getStep(Long stepId);

    /**
     * 根据任务ID获取步骤（别名方法）
     * Get steps by task ID (alias method)
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 步骤列表 / List of steps
     */
    List<TaskStep> getStepsByTask(Long agentTaskId);

    /**
     * 根据任务ID和状态获取步骤
     * Get steps by task ID and status
     *
     * @param agentTaskId 任务ID / Task ID
     * @param status 步骤状态 / Step status
     * @return 步骤列表 / List of steps
     */
    List<TaskStep> getStepsByStatusAndTask(Long agentTaskId, TaskStepStatus status);

    /**
     * 设置步骤输入数据
     * Set step input data
     *
     * @param stepId 步骤ID / Step ID
     * @param inputData 输入数据 / Input data
     */
    void setInput(Long stepId, String inputData);

    /**
     * 更新步骤状态（别名方法）
     * Update step status (alias method)
     *
     * @param stepId 步骤ID / Step ID
     * @param status 新状态 / New status
     */
    void updateStatus(Long stepId, TaskStepStatus status);

    /**
     * 增加重试计数
     * Increment retry count
     *
     * @param stepId 步骤ID / Step ID
     */
    void incrementRetryCount(Long stepId);

    /**
     * 获取任务的步骤（按顺序）
     * Get task steps (ordered)
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 排序的步骤列表 / Ordered list of steps
     */
    List<TaskStep> getTaskStepsOrdered(Long agentTaskId);

    /**
     * 获取指定状态的步骤
     * Get steps with specified status
     *
     * @param agentTaskId 任务ID / Task ID
     * @param status 步骤状态 / Step status
     * @return 步骤列表 / List of steps
     */
    List<TaskStep> getStepsByStatus(Long agentTaskId, TaskStepStatus status);

    /**
     * 获取可执行的步骤（依赖已完成）
     * Get executable steps (dependencies completed)
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 可执行的步骤列表 / List of executable steps
     */
    List<TaskStep> getExecutableSteps(Long agentTaskId);

    /**
     * 检查步骤是否可以执行（依赖检查）
     * Check if step can be executed (dependency check)
     *
     * @param stepId 步骤ID / Step ID
     * @return 是否可以执行 / Whether can execute
     */
    boolean canExecuteStep(Long stepId);

    /**
     * 更新步骤元数据
     * Update step metadata
     *
     * @param stepId 步骤ID / Step ID
     * @param metadata 元数据（JSON格式）/ Metadata (JSON format)
     */
    void updateStepMetadata(Long stepId, String metadata);

    /**
     * 获取任务的执行进度
     * Get task execution progress
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 执行进度（0-100）/ Execution progress (0-100)
     */
    int getTaskProgress(Long agentTaskId);

    /**
     * 获取步骤执行统计信息
     * Get step execution statistics
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 统计信息映射 / Statistics map
     */
    java.util.Map<String, Object> getTaskStepStatistics(Long agentTaskId);
}
