/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：Agent任务响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.Instant;

/**
 * Agent任务响应DTO
 * <p>
 * 用于返回Agent任务的执行结果和状态信息。
 * 包含任务的完整执行信息。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "Agent任务响应")
public class AgentTaskResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", example = "1")
    private Long taskId;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型", example = "REGULATION_INTERNALIZATION", allowableValues = {"REGULATION_INTERNALIZATION", "POLICY_REVIEW", "CONTRACT_REVIEW"})
    private String taskType;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题", example = "电力行业安全生产法规内化")
    private String title;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "COMPLETED", allowableValues = {"PENDING", "RUNNING", "COMPLETED", "FAILED", "CANCELLED"})
    private String status;

    /**
     * 进度百分比
     */
    @Schema(description = "进度百分比", example = "100")
    private Integer progress;

    /**
     * 任务结果
     */
    @Schema(description = "任务结果（JSON格式）")
    private String result;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Instant endTime;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)", example = "5000")
    private Long executionTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Instant createdAt;

    // 默认构造函数
    public AgentTaskResponseDTO() {}

    // 全参构造函数
    public AgentTaskResponseDTO(
        Long taskId,
        String taskType,
        String title,
        String status,
        Integer progress,
        String result,
        Instant startTime,
        Instant endTime,
        Long executionTime,
        String errorMessage,
        Instant createdAt
    ) {
        this.taskId = taskId;
        this.taskType = taskType;
        this.title = title;
        this.status = status;
        this.progress = progress;
        this.result = result;
        this.startTime = startTime;
        this.endTime = endTime;
        this.executionTime = executionTime;
        this.errorMessage = errorMessage;
        this.createdAt = createdAt;
    }

    // Getter和Setter方法
    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Instant getStartTime() {
        return startTime;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getEndTime() {
        return endTime;
    }

    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    // Builder模式的静态方法
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private Long taskId;
        private String taskType;
        private String title;
        private String status;
        private Integer progress;
        private String result;
        private Instant startTime;
        private Instant endTime;
        private Long executionTime;
        private String errorMessage;
        private Instant createdAt;

        public Builder taskId(Long taskId) {
            this.taskId = taskId;
            return this;
        }

        public Builder taskType(String taskType) {
            this.taskType = taskType;
            return this;
        }

        public Builder title(String title) {
            this.title = title;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder progress(Integer progress) {
            this.progress = progress;
            return this;
        }

        public Builder result(String result) {
            this.result = result;
            return this;
        }

        public Builder startTime(Instant startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder endTime(Instant endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder executionTime(Long executionTime) {
            this.executionTime = executionTime;
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public AgentTaskResponseDTO build() {
            return new AgentTaskResponseDTO(
                taskId, taskType, title, status, progress, result,
                startTime, endTime, executionTime, errorMessage, createdAt
            );
        }
    }

    @Override
    public String toString() {
        return "AgentTaskResponseDTO{" +
            "taskId=" + taskId +
            ", taskType='" + taskType + '\'' +
            ", title='" + title + '\'' +
            ", status='" + status + '\'' +
            ", progress=" + progress +
            ", result='" + result + '\'' +
            ", startTime=" + startTime +
            ", endTime=" + endTime +
            ", executionTime=" + executionTime +
            ", errorMessage='" + errorMessage + '\'' +
            ", createdAt=" + createdAt +
            '}';
    }
}
