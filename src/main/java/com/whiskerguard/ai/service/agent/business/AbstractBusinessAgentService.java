/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AbstractBusinessAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent.business
 * 描    述：业务智能体服务抽象基类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.business;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.service.agent.core.AgentConfigCoreService;
import com.whiskerguard.ai.service.agent.core.AgentContextCoreService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorServiceInterface;
import com.whiskerguard.ai.service.agent.core.TaskStepCoreService;
import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import com.whiskerguard.ai.service.agent.core.workflow.WorkflowDefinitionFactory;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 业务智能体服务抽象基类
 * <p>
 * 为所有业务Agent服务提供统一的基础功能，包括：
 * 1. 上下文管理 - 统一的AgentContext创建、更新和查询
 * 2. 工作流编排 - 基于TaskOrchestrator的标准化流程执行
 * 3. 配置管理 - 集成AgentConfig的配置获取和应用
 * 4. 步骤管理 - TaskStep的创建、状态管理和监控
 * 5. 错误处理 - 标准化的异常处理和重试机制
 *
 * <p>业务Agent服务继承此类后，只需关注具体的业务逻辑实现，
 * 框架层面的功能由基类统一提供。
 *
 * <AUTHOR>
 * @since 1.0
 */
public abstract class AbstractBusinessAgentService {

    private static final Logger log = LoggerFactory.getLogger(AbstractBusinessAgentService.class);

    @Autowired
    protected AgentContextCoreService agentContextCoreService;

    @Autowired
    protected TaskStepCoreService taskStepCoreService;

    @Autowired
    protected AgentConfigCoreService agentConfigCoreService;

    @Autowired
    protected TaskOrchestratorServiceInterface taskOrchestratorService;

    /**
     * 创建业务处理上下文
     * Context Creation - 为业务任务创建专门的执行上下文
     *
     * @param tenantId 租户ID
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param businessData 业务数据
     * @return AgentContext实例
     */
    protected AgentContext createBusinessContext(String tenantId, Long taskId, AgentTaskType taskType, Map<String, Object> businessData) {
        log.debug("创建业务处理上下文 - 租户: {}, 任务: {}, 类型: {}", tenantId, taskId, taskType);

        try {
            // 创建基础上下文
            AgentContext context = agentContextCoreService.createContext(taskId, taskType != null ? taskType.name() : "DEFAULT");

            // 存储业务数据
            if (businessData != null) {
                for (Map.Entry<String, Object> entry : businessData.entrySet()) {
                    agentContextCoreService.storeVariable(context.getId(), entry.getKey(), entry.getValue());
                }
            }

            // 设置基础变量
            agentContextCoreService.storeVariable(context.getId(), "tenantId", tenantId);
            agentContextCoreService.storeVariable(context.getId(), "startTime", Instant.now().toString());
            agentContextCoreService.storeVariable(context.getId(), "processId", UUID.randomUUID().toString());
            agentContextCoreService.storeVariable(context.getId(), "businessClass", this.getClass().getSimpleName());

            log.info("成功创建业务处理上下文 - ID: {}", context.getId());
            return context;
        } catch (Exception e) {
            log.error("创建业务处理上下文失败", e);
            throw new RuntimeException("Failed to create business context: " + e.getMessage(), e);
        }
    }

    /**
     * 执行标准化业务工作流
     * Workflow Execution - 基于预定义工作流执行业务流程
     *
     * @param context 执行上下文
     * @param taskType 任务类型
     * @param inputData 输入数据
     * @return 执行结果
     */
    protected Map<String, Object> executeStandardWorkflow(AgentContext context, AgentTaskType taskType, Map<String, Object> inputData) {
        log.debug("执行标准化业务工作流 - 上下文: {}, 任务类型: {}", context.getId(), taskType);

        try {
            // 获取工作流定义 - 根据任务类型选择合适的工作流
            List<StepDefinition> workflow;
            switch (taskType) {
                case CONTRACT_REVIEW:
                    workflow = WorkflowDefinitionFactory.getContractReviewWorkflow();
                    break;
                case POLICY_REVIEW:
                    workflow = WorkflowDefinitionFactory.getPolicyReviewWorkflow();
                    break;
                case REGULATION_INTERNALIZATION:
                    workflow = WorkflowDefinitionFactory.getRegulationInternalizationWorkflow();
                    break;
                default:
                    log.warn("未找到任务类型 {} 的工作流定义，使用自定义工作流", taskType);
                    workflow = getCustomWorkflowDefinition(taskType);
                    break;
            }

            // 更新上下文状态
            agentContextCoreService.storeVariable(context.getId(), "workflowStatus", "EXECUTING");

            // 执行工作流 - 使用智能并行执行
            executeWorkflowWithOptimalParallelism(context.getAgentTaskId(), workflow);

            // 从步骤结果中获取最终结果
            Map<String, Object> result = new HashMap<>();
            result.put("status", "completed");
            result.put("workflowType", taskType.name());
            result.put("executionTime", Instant.now().toString());

            // 更新上下文状态和结果
            agentContextCoreService.storeVariable(context.getId(), "workflowStatus", "COMPLETED");
            agentContextCoreService.storeVariable(context.getId(), "workflowResult", result);

            log.info("标准化业务工作流执行完成 - 上下文: {}", context.getId());
            return result;
        } catch (Exception e) {
            log.error("执行标准化业务工作流失败 - 上下文: {}", context.getId(), e);
            agentContextCoreService.storeVariable(context.getId(), "workflowStatus", "FAILED");
            throw new RuntimeException("Failed to execute workflow: " + e.getMessage(), e);
        }
    }

    /**
     * 获取业务配置
     * Configuration Retrieval - 获取特定业务的配置参数
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @param tenantId 租户ID
     * @param taskType 任务类型
     * @return 配置值
     */
    protected <T> T getBusinessConfig(String configKey, T defaultValue, Long tenantId, AgentTaskType taskType) {
        try {
            return agentConfigCoreService.getConfig(configKey, defaultValue, tenantId, taskType);
        } catch (Exception e) {
            log.warn("获取业务配置失败，使用默认值 - Key: {}, Default: {}", configKey, defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 获取字符串配置
     * String Configuration Retrieval - 获取字符串类型的配置参数
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @param taskType 任务类型
     * @return 配置值（可选）
     */
    protected Optional<String> getStringConfig(String configKey, Long tenantId, AgentTaskType taskType) {
        try {
            return agentConfigCoreService.getStringConfig(configKey, tenantId, taskType);
        } catch (Exception e) {
            log.warn("获取字符串配置失败 - Key: {}", configKey, e);
            return Optional.empty();
        }
    }

    /**
     * 更新上下文进度
     * Progress Update - 更新业务处理进度和状态信息
     *
     * @param context 上下文
     * @param progress 进度百分比 (0-100)
     * @param status 状态描述
     * @param additionalInfo 附加信息
     */
    protected void updateBusinessProgress(AgentContext context, int progress, String status, Map<String, Object> additionalInfo) {
        try {
            // 更新基础进度信息
            agentContextCoreService.storeVariable(context.getId(), "progress", progress);
            agentContextCoreService.storeVariable(context.getId(), "status", status);
            agentContextCoreService.storeVariable(context.getId(), "lastUpdate", Instant.now().toString());

            // 更新附加信息
            if (additionalInfo != null) {
                for (Map.Entry<String, Object> entry : additionalInfo.entrySet()) {
                    agentContextCoreService.storeVariable(context.getId(), entry.getKey(), entry.getValue());
                }
            }

            log.debug("更新业务进度 - 上下文: {}, 进度: {}%, 状态: {}", context.getId(), progress, status);
        } catch (Exception e) {
            log.warn("更新业务进度失败", e);
        }
    }

    /**
     * 处理业务异常
     * Exception Handling - 标准化的业务异常处理
     *
     * @param context 上下文
     * @param exception 异常
     * @param phase 出错阶段
     */
    protected void handleBusinessException(AgentContext context, Exception exception, String phase) {
        log.error("业务处理异常 - 上下文: {}, 阶段: {}", context.getId(), phase, exception);

        try {
            // 更新上下文状态
            agentContextCoreService.storeVariable(context.getId(), "status", "ERROR");
            agentContextCoreService.storeVariable(context.getId(), "errorPhase", phase);
            agentContextCoreService.storeVariable(context.getId(), "errorMessage", exception.getMessage());
            agentContextCoreService.storeVariable(context.getId(), "errorTime", Instant.now().toString());

            // 记录异常历史
            agentContextCoreService.recordHistory(
                context.getId(),
                "ERROR",
                String.format("Phase: %s, Error: %s", phase, exception.getMessage())
            );
        } catch (Exception e) {
            log.error("处理业务异常时发生错误", e);
        }
    }

    /**
     * 获取上下文信息
     * Context Retrieval - 获取指定任务的上下文信息
     *
     * @param taskId 任务ID
     * @return 上下文信息（可选）
     */
    protected Optional<AgentContext> getBusinessContext(Long taskId) {
        try {
            AgentContext context = agentContextCoreService.getContextByTaskId(taskId);
            return Optional.of(context);
        } catch (Exception e) {
            log.warn("获取业务上下文失败 - 任务ID: {}", taskId, e);
            return Optional.empty();
        }
    }

    /**
     * 完成业务处理
     * Business Completion - 标记业务处理完成并清理资源
     *
     * @param context 上下文
     * @param result 处理结果
     */
    protected void completeBusinessProcessing(AgentContext context, Map<String, Object> result) {
        log.debug("完成业务处理 - 上下文: {}", context.getId());

        try {
            // 更新最终状态
            agentContextCoreService.storeVariable(context.getId(), "status", "COMPLETED");
            agentContextCoreService.storeVariable(context.getId(), "completedAt", Instant.now().toString());
            agentContextCoreService.storeVariable(context.getId(), "finalResult", result);

            // 记录完成历史
            agentContextCoreService.recordHistory(context.getId(), "COMPLETED", "Business processing completed successfully");

            log.info("业务处理完成 - 上下文: {}", context.getId());
        } catch (Exception e) {
            log.error("完成业务处理时发生错误", e);
        }
    }

    /**
     * 获取自定义工作流定义
     * Custom Workflow Definition - 子类可重写此方法提供自定义工作流
     *
     * @param taskType 任务类型
     * @return 工作流步骤定义列表
     */
    protected abstract List<StepDefinition> getCustomWorkflowDefinition(AgentTaskType taskType);

    /**
     * 获取业务Agent类型
     * Business Agent Type - 返回当前业务Agent的类型标识
     *
     * @return Agent类型名称
     */
    protected String getBusinessAgentType() {
        return this.getClass().getSimpleName().replace("Service", "");
    }
}
