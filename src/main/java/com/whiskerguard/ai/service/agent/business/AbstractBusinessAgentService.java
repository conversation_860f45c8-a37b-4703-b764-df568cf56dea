/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AbstractBusinessAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent.business
 * 描    述：业务智能体服务抽象基类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.business;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.service.agent.core.AgentConfigCoreService;
import com.whiskerguard.ai.service.agent.core.AgentContextCoreService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorService;
import com.whiskerguard.ai.service.agent.core.TaskStepCoreService;
import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import com.whiskerguard.ai.service.agent.core.workflow.WorkflowDefinitionFactory;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 业务智能体服务抽象基类
 * <p>
 * 为所有业务Agent服务提供统一的基础功能，包括：
 * 1. 上下文管理 - 统一的AgentContext创建、更新和查询
 * 2. 工作流编排 - 基于TaskOrchestrator的标准化流程执行
 * 3. 配置管理 - 集成AgentConfig的配置获取和应用
 * 4. 步骤管理 - TaskStep的创建、状态管理和监控
 * 5. 错误处理 - 标准化的异常处理和重试机制
 *
 * <p>业务Agent服务继承此类后，只需关注具体的业务逻辑实现，
 * 框架层面的功能由基类统一提供。
 *
 * <AUTHOR>
 * @since 1.0
 */
public abstract class AbstractBusinessAgentService {

    private static final Logger log = LoggerFactory.getLogger(AbstractBusinessAgentService.class);

    @Autowired
    protected AgentContextCoreService agentContextCoreService;

    @Autowired
    protected TaskStepCoreService taskStepCoreService;

    @Autowired
    protected AgentConfigCoreService agentConfigCoreService;

    @Autowired
    protected TaskOrchestratorService taskOrchestratorService;

    /**
     * 创建业务处理上下文
     * Context Creation - 为业务任务创建专门的执行上下文
     *
     * @param tenantId 租户ID
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param businessData 业务数据
     * @return AgentContext实例
     */
    protected AgentContext createBusinessContext(String tenantId, Long taskId, AgentTaskType taskType, Map<String, Object> businessData) {
        log.debug("创建业务处理上下文 - 租户: {}, 任务: {}, 类型: {}", tenantId, taskId, taskType);

        try {
            // 创建基础上下文
            AgentContext context = agentContextCoreService.createContext(taskId, taskType != null ? taskType.name() : "DEFAULT");

            // 存储业务数据
            if (businessData != null) {
                for (Map.Entry<String, Object> entry : businessData.entrySet()) {
                    agentContextCoreService.storeVariable(context.getId(), entry.getKey(), entry.getValue());
                }
            }

            // 设置基础变量
            agentContextCoreService.storeVariable(context.getId(), "tenantId", tenantId);
            agentContextCoreService.storeVariable(context.getId(), "startTime", Instant.now().toString());
            agentContextCoreService.storeVariable(context.getId(), "processId", UUID.randomUUID().toString());
            agentContextCoreService.storeVariable(context.getId(), "businessClass", this.getClass().getSimpleName());

            log.info("成功创建业务处理上下文 - ID: {}", context.getId());
            return context;
        } catch (Exception e) {
            log.error("创建业务处理上下文失败", e);
            throw new RuntimeException("Failed to create business context: " + e.getMessage(), e);
        }
    }

    /**
     * 执行标准化业务工作流
     * Workflow Execution - 基于预定义工作流执行业务流程
     *
     * @param context 执行上下文
     * @param taskType 任务类型
     * @param inputData 输入数据
     * @return 执行结果
     */
    protected Map<String, Object> executeStandardWorkflow(AgentContext context, AgentTaskType taskType, Map<String, Object> inputData) {
        log.debug("执行标准化业务工作流 - 上下文: {}, 任务类型: {}", context.getId(), taskType);

        try {
            // 获取工作流定义 - 根据任务类型选择合适的工作流
            List<StepDefinition> workflow;
            switch (taskType) {
                case CONTRACT_REVIEW:
                    workflow = WorkflowDefinitionFactory.getContractReviewWorkflow();
                    break;
                case POLICY_REVIEW:
                    workflow = WorkflowDefinitionFactory.getPolicyReviewWorkflow();
                    break;
                case REGULATION_INTERNALIZATION:
                    workflow = WorkflowDefinitionFactory.getRegulationInternalizationWorkflow();
                    break;
                default:
                    log.warn("未找到任务类型 {} 的工作流定义，使用自定义工作流", taskType);
                    workflow = getCustomWorkflowDefinition(taskType);
                    break;
            }

            // 更新上下文状态
            agentContextCoreService.storeVariable(context.getId(), "workflowStatus", "EXECUTING");

            // 执行工作流 - 使用智能并行执行
            executeWorkflowWithOptimalParallelism(context.getAgentTaskId(), workflow);

            // 从步骤结果中获取最终结果
            Map<String, Object> result = new HashMap<>();
            result.put("status", "completed");
            result.put("workflowType", taskType.name());
            result.put("executionTime", Instant.now().toString());

            // 更新上下文状态和结果
            agentContextCoreService.storeVariable(context.getId(), "workflowStatus", "COMPLETED");
            agentContextCoreService.storeVariable(context.getId(), "workflowResult", result);

            log.info("标准化业务工作流执行完成 - 上下文: {}", context.getId());
            return result;
        } catch (Exception e) {
            log.error("执行标准化业务工作流失败 - 上下文: {}", context.getId(), e);
            agentContextCoreService.storeVariable(context.getId(), "workflowStatus", "FAILED");
            throw new RuntimeException("Failed to execute workflow: " + e.getMessage(), e);
        }
    }

    /**
     * 获取业务配置
     * Configuration Retrieval - 获取特定业务的配置参数
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @param tenantId 租户ID
     * @param taskType 任务类型
     * @return 配置值
     */
    protected <T> T getBusinessConfig(String configKey, T defaultValue, Long tenantId, AgentTaskType taskType) {
        try {
            return agentConfigCoreService.getConfig(configKey, defaultValue, tenantId, taskType);
        } catch (Exception e) {
            log.warn("获取业务配置失败，使用默认值 - Key: {}, Default: {}", configKey, defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 获取字符串配置
     * String Configuration Retrieval - 获取字符串类型的配置参数
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @param taskType 任务类型
     * @return 配置值（可选）
     */
    protected Optional<String> getStringConfig(String configKey, Long tenantId, AgentTaskType taskType) {
        try {
            return agentConfigCoreService.getStringConfig(configKey, tenantId, taskType);
        } catch (Exception e) {
            log.warn("获取字符串配置失败 - Key: {}", configKey, e);
            return Optional.empty();
        }
    }

    /**
     * 更新上下文进度
     * Progress Update - 更新业务处理进度和状态信息
     *
     * @param context 上下文
     * @param progress 进度百分比 (0-100)
     * @param status 状态描述
     * @param additionalInfo 附加信息
     */
    protected void updateBusinessProgress(AgentContext context, int progress, String status, Map<String, Object> additionalInfo) {
        try {
            // 更新基础进度信息
            agentContextCoreService.storeVariable(context.getId(), "progress", progress);
            agentContextCoreService.storeVariable(context.getId(), "status", status);
            agentContextCoreService.storeVariable(context.getId(), "lastUpdate", Instant.now().toString());

            // 更新附加信息
            if (additionalInfo != null) {
                for (Map.Entry<String, Object> entry : additionalInfo.entrySet()) {
                    agentContextCoreService.storeVariable(context.getId(), entry.getKey(), entry.getValue());
                }
            }

            log.debug("更新业务进度 - 上下文: {}, 进度: {}%, 状态: {}", context.getId(), progress, status);
        } catch (Exception e) {
            log.warn("更新业务进度失败", e);
        }
    }

    /**
     * 处理业务异常
     * Exception Handling - 标准化的业务异常处理
     *
     * @param context 上下文
     * @param exception 异常
     * @param phase 出错阶段
     */
    protected void handleBusinessException(AgentContext context, Exception exception, String phase) {
        log.error("业务处理异常 - 上下文: {}, 阶段: {}", context.getId(), phase, exception);

        try {
            // 更新上下文状态
            agentContextCoreService.storeVariable(context.getId(), "status", "ERROR");
            agentContextCoreService.storeVariable(context.getId(), "errorPhase", phase);
            agentContextCoreService.storeVariable(context.getId(), "errorMessage", exception.getMessage());
            agentContextCoreService.storeVariable(context.getId(), "errorTime", Instant.now().toString());

            // 记录异常历史
            agentContextCoreService.recordHistory(
                context.getId(),
                "ERROR",
                String.format("Phase: %s, Error: %s", phase, exception.getMessage())
            );
        } catch (Exception e) {
            log.error("处理业务异常时发生错误", e);
        }
    }

    /**
     * 获取上下文信息
     * Context Retrieval - 获取指定任务的上下文信息
     *
     * @param taskId 任务ID
     * @return 上下文信息（可选）
     */
    protected Optional<AgentContext> getBusinessContext(Long taskId) {
        try {
            AgentContext context = agentContextCoreService.getContextByTaskId(taskId);
            return Optional.of(context);
        } catch (Exception e) {
            log.warn("获取业务上下文失败 - 任务ID: {}", taskId, e);
            return Optional.empty();
        }
    }

    /**
     * 完成业务处理
     * Business Completion - 标记业务处理完成并清理资源
     *
     * @param context 上下文
     * @param result 处理结果
     */
    protected void completeBusinessProcessing(AgentContext context, Map<String, Object> result) {
        log.debug("完成业务处理 - 上下文: {}", context.getId());

        try {
            // 更新最终状态
            agentContextCoreService.storeVariable(context.getId(), "status", "COMPLETED");
            agentContextCoreService.storeVariable(context.getId(), "completedAt", Instant.now().toString());
            agentContextCoreService.storeVariable(context.getId(), "finalResult", result);

            // 记录完成历史
            agentContextCoreService.recordHistory(context.getId(), "COMPLETED", "Business processing completed successfully");

            log.info("业务处理完成 - 上下文: {}", context.getId());
        } catch (Exception e) {
            log.error("完成业务处理时发生错误", e);
        }
    }

    /**
     * 获取自定义工作流定义
     * Custom Workflow Definition - 子类可重写此方法提供自定义工作流
     *
     * @param taskType 任务类型
     * @return 工作流步骤定义列表
     */
    protected abstract List<StepDefinition> getCustomWorkflowDefinition(AgentTaskType taskType);

    /**
     * 智能并行执行工作流
     * <p>
     * 根据步骤依赖关系自动识别可并行执行的步骤，最大化执行效率。
     * 对于没有依赖关系的步骤（如检索类步骤），会并行执行以节省时间。
     *
     * @param agentTaskId 任务ID
     * @param workflow 工作流步骤定义
     */
    protected void executeWorkflowWithOptimalParallelism(Long agentTaskId, List<StepDefinition> workflow) {
        log.info("开始智能并行执行工作流，任务ID: {}, 总步骤数: {}", agentTaskId, workflow.size());

        try {
            // 分析步骤依赖关系，识别可并行执行的步骤组
            List<List<StepDefinition>> parallelGroups = analyzeStepDependencies(workflow);

            log.info("工作流分析完成，任务ID: {}, 并行组数: {}", agentTaskId, parallelGroups.size());

            // 按组执行，组内并行，组间串行
            for (int groupIndex = 0; groupIndex < parallelGroups.size(); groupIndex++) {
                List<StepDefinition> parallelSteps = parallelGroups.get(groupIndex);

                log.info("执行第{}组并行步骤，任务ID: {}, 步骤数: {}",
                    groupIndex + 1, agentTaskId, parallelSteps.size());

                if (parallelSteps.size() == 1) {
                    // 单个步骤，直接执行
                    taskOrchestratorService.executeStep(agentTaskId, parallelSteps.get(0));
                } else {
                    // 多个步骤，并行执行
                    taskOrchestratorService.executeWorkflowParallel(agentTaskId, parallelSteps);
                }

                log.info("第{}组并行步骤执行完成，任务ID: {}", groupIndex + 1, agentTaskId);
            }

            log.info("智能并行工作流执行完成，任务ID: {}", agentTaskId);
        } catch (Exception e) {
            log.error("智能并行工作流执行失败，任务ID: {}", agentTaskId, e);
            throw new RuntimeException("智能并行工作流执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分析步骤依赖关系，识别可并行执行的步骤组
     * <p>
     * 根据步骤类型和依赖关系，将工作流分解为多个并行组：
     * - 检索类步骤（RETRIEVAL）通常可以并行执行
     * - 分析类步骤（ANALYSIS）依赖检索结果，需要在检索完成后执行
     * - 生成类步骤（LLM）依赖分析结果，需要在分析完成后执行
     *
     * @param workflow 原始工作流步骤
     * @return 并行执行组列表
     */
    protected List<List<StepDefinition>> analyzeStepDependencies(List<StepDefinition> workflow) {
        List<List<StepDefinition>> parallelGroups = new ArrayList<>();

        // 第一组：所有检索类步骤（可以并行执行）
        List<StepDefinition> retrievalSteps = workflow.stream()
            .filter(step -> "RETRIEVAL".equals(step.getType()) ||
                           step.getName().contains("检索") ||
                           step.getName().contains("retrieval"))
            .collect(Collectors.toList());

        if (!retrievalSteps.isEmpty()) {
            parallelGroups.add(retrievalSteps);
            log.debug("识别到{}个检索步骤可并行执行", retrievalSteps.size());
        }

        // 第二组：所有分析类步骤（依赖检索结果）
        List<StepDefinition> analysisSteps = workflow.stream()
            .filter(step -> "ANALYSIS".equals(step.getType()) ||
                           step.getName().contains("分析") ||
                           step.getName().contains("analysis"))
            .collect(Collectors.toList());

        if (!analysisSteps.isEmpty()) {
            parallelGroups.add(analysisSteps);
            log.debug("识别到{}个分析步骤可并行执行", analysisSteps.size());
        }

        // 第三组：所有生成类步骤（依赖分析结果）
        List<StepDefinition> generationSteps = workflow.stream()
            .filter(step -> "LLM".equals(step.getType()) ||
                           step.getName().contains("生成") ||
                           step.getName().contains("generation"))
            .collect(Collectors.toList());

        if (!generationSteps.isEmpty()) {
            parallelGroups.add(generationSteps);
            log.debug("识别到{}个生成步骤可并行执行", generationSteps.size());
        }

        // 第四组：其他步骤（验证、整合等）
        List<StepDefinition> otherSteps = workflow.stream()
            .filter(step -> !retrievalSteps.contains(step) &&
                           !analysisSteps.contains(step) &&
                           !generationSteps.contains(step))
            .collect(Collectors.toList());

        if (!otherSteps.isEmpty()) {
            parallelGroups.add(otherSteps);
            log.debug("识别到{}个其他步骤", otherSteps.size());
        }

        return parallelGroups;
    }

    /**
     * 获取业务Agent类型
     * Business Agent Type - 返回当前业务Agent的类型标识
     *
     * @return Agent类型名称
     */
    protected String getBusinessAgentType() {
        return this.getClass().getSimpleName().replace("Service", "");
    }
}
