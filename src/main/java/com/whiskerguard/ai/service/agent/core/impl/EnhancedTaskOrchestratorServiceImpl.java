package com.whiskerguard.ai.service.agent.core.impl;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.service.agent.core.AgentContextCoreService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorServiceInterface;
import com.whiskerguard.ai.service.agent.core.TaskStepCoreService;
import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import com.whiskerguard.ai.service.agent.core.processor.StepProcessor;
import com.whiskerguard.ai.service.agent.core.processor.StepProcessorFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 增强版任务编排服务实现
 * Enhanced Task Orchestrator Service Implementation
 *
 * 提供完整的工作流编排和执行功能
 * Provide complete workflow orchestration and execution capabilities
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service("enhancedTaskOrchestratorService")
@Transactional

public class EnhancedTaskOrchestratorServiceImpl implements TaskOrchestratorServiceInterface {

    private static final Logger log = LoggerFactory.getLogger(EnhancedTaskOrchestratorServiceImpl.class);

    private final TaskStepCoreService taskStepService;
    private final AgentContextCoreService contextService;
    private final StepProcessorFactory processorFactory;
    private final Executor asyncExecutor;

    // 存储正在执行的工作流
    private final Map<Long, CompletableFuture<Void>> runningWorkflows = new ConcurrentHashMap<>();

    public EnhancedTaskOrchestratorServiceImpl(
        TaskStepCoreService taskStepService,
        AgentContextCoreService contextService,
        StepProcessorFactory processorFactory,
        @Qualifier("taskExecutor") Executor asyncExecutor
    ) {
        this.taskStepService = taskStepService;
        this.contextService = contextService;
        this.processorFactory = processorFactory;
        this.asyncExecutor = asyncExecutor;
    }

    @Override
    public void executeWorkflow(Long agentTaskId, List<StepDefinition> steps) {
        log.info("开始执行工作流，任务ID: {}, 步骤数: {}", agentTaskId, steps.size());

        try {
            // 获取上下文
            AgentContext context = contextService.getContextByTaskId(agentTaskId);

            // 按顺序执行步骤
            for (StepDefinition stepDef : steps) {
                executeStepInternal(agentTaskId, stepDef, context);
            }

            log.info("工作流执行完成，任务ID: {}", agentTaskId);
        } catch (Exception e) {
            log.error("工作流执行失败，任务ID: {}", agentTaskId, e);
            throw new RuntimeException("工作流执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CompletableFuture<Void> executeWorkflowAsync(Long agentTaskId, List<StepDefinition> steps) {
        log.info("开始异步执行工作流，任务ID: {}, 步骤数: {}", agentTaskId, steps.size());

        CompletableFuture<Void> future = CompletableFuture.runAsync(
            () -> {
                executeWorkflow(agentTaskId, steps);
            },
            asyncExecutor
        );

        // 存储正在执行的工作流
        runningWorkflows.put(agentTaskId, future);

        // 完成后清理
        future.whenComplete((result, throwable) -> {
            runningWorkflows.remove(agentTaskId);
            if (throwable != null) {
                log.error("异步工作流执行失败，任务ID: {}", agentTaskId, throwable);
            } else {
                log.info("异步工作流执行完成，任务ID: {}", agentTaskId);
            }
        });

        return future;
    }

    @Override
    public void executeWorkflowParallel(Long agentTaskId, List<StepDefinition> parallelSteps) {
        log.info("开始并行执行工作流步骤，任务ID: {}, 并行步骤数: {}", agentTaskId, parallelSteps.size());

        try {
            // 获取上下文
            AgentContext context = contextService.getContextByTaskId(agentTaskId);

            // 创建并行任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (StepDefinition stepDef : parallelSteps) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> {
                        executeStepInternal(agentTaskId, stepDef, context);
                    },
                    asyncExecutor
                );
                futures.add(future);
            }

            // 等待所有并行步骤完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("并行工作流步骤执行完成，任务ID: {}", agentTaskId);
        } catch (Exception e) {
            log.error("并行工作流步骤执行失败，任务ID: {}", agentTaskId, e);
            throw new RuntimeException("并行工作流步骤执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String executeStep(Long agentTaskId, StepDefinition stepDefinition) {
        log.debug("执行单个步骤，任务ID: {}, 步骤: {}", agentTaskId, stepDefinition.getName());

        try {
            // 获取上下文
            AgentContext context = contextService.getContextByTaskId(agentTaskId);

            return executeStepInternal(agentTaskId, stepDefinition, context);
        } catch (Exception e) {
            log.error("步骤执行失败，任务ID: {}, 步骤: {}", agentTaskId, stepDefinition.getName(), e);
            throw new RuntimeException("步骤执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean retryStep(Long stepId) {
        log.info("重试步骤，步骤ID: {}", stepId);

        try {
            // 获取步骤信息
            List<TaskStep> steps = taskStepService.getTaskSteps(stepId);
            TaskStep step = steps.stream().filter(s -> s.getId().equals(stepId)).findFirst().orElse(null);
            if (step == null) {
                log.warn("步骤不存在，步骤ID: {}", stepId);
                return false;
            }

            // 检查步骤状态
            if (step.getStatus() != TaskStepStatus.FAILED) {
                log.warn("步骤状态不是失败，无法重试，步骤ID: {}, 状态: {}", stepId, step.getStatus());
                return false;
            }

            // 增加重试计数（暂时通过更新步骤来实现）
            // TODO: 实现专门的重试计数方法
            // taskStepService.incrementRetryCount(stepId);

            // 重新设置状态为运行中
            taskStepService.updateStepStatus(stepId, TaskStepStatus.RUNNING);

            // 这里需要重新执行步骤的逻辑，但需要更多上下文信息
            // 实际实现中可能需要保存更多步骤执行信息

            log.info("步骤重试成功，步骤ID: {}", stepId);
            return true;
        } catch (Exception e) {
            log.error("步骤重试失败，步骤ID: {}", stepId, e);
            return false;
        }
    }

    @Override
    public boolean cancelWorkflow(Long agentTaskId) {
        log.info("取消工作流，任务ID: {}", agentTaskId);

        try {
            // 取消正在运行的异步工作流
            CompletableFuture<Void> runningWorkflow = runningWorkflows.get(agentTaskId);
            if (runningWorkflow != null) {
                runningWorkflow.cancel(true);
                runningWorkflows.remove(agentTaskId);
            }

            // 更新所有运行中的步骤状态为取消
            List<TaskStep> runningSteps = taskStepService.getStepsByStatus(agentTaskId, TaskStepStatus.RUNNING);

            for (TaskStep step : runningSteps) {
                taskStepService.updateStepStatus(step.getId(), TaskStepStatus.CANCELLED);
            }

            log.info("工作流取消成功，任务ID: {}", agentTaskId);
            return true;
        } catch (Exception e) {
            log.error("工作流取消失败，任务ID: {}", agentTaskId, e);
            return false;
        }
    }

    @Override
    public WorkflowStatus getWorkflowStatus(Long agentTaskId) {
        try {
            List<TaskStep> steps = taskStepService.getTaskSteps(agentTaskId);

            boolean isRunning =
                runningWorkflows.containsKey(agentTaskId) || steps.stream().anyMatch(step -> step.getStatus() == TaskStepStatus.RUNNING);

            int totalSteps = steps.size();
            int completedSteps = (int) steps.stream().mapToLong(step -> step.getStatus() == TaskStepStatus.COMPLETED ? 1 : 0).sum();
            int failedSteps = (int) steps.stream().mapToLong(step -> step.getStatus() == TaskStepStatus.FAILED ? 1 : 0).sum();

            boolean isCompleted = !isRunning && completedSteps == totalSteps;
            boolean isFailed = failedSteps > 0;

            // 获取最后一个错误
            String lastError = steps
                .stream()
                .filter(step -> step.getStatus() == TaskStepStatus.FAILED)
                .filter(step -> step.getErrorMessage() != null)
                .reduce((first, second) -> second) // 获取最后一个
                .map(TaskStep::getErrorMessage)
                .orElse(null);

            return new WorkflowStatus(isRunning, isCompleted, isFailed, totalSteps, completedSteps, failedSteps, lastError);
        } catch (Exception e) {
            log.error("获取工作流状态失败，任务ID: {}", agentTaskId, e);
            return new WorkflowStatus(false, false, true, 0, 0, 0, e.getMessage());
        }
    }

    @Override
    public boolean resumeWorkflow(Long agentTaskId) {
        log.info("恢复工作流，任务ID: {}", agentTaskId);

        try {
            // 获取所有失败的步骤
            List<TaskStep> failedSteps = taskStepService.getStepsByStatus(agentTaskId, TaskStepStatus.FAILED);

            if (failedSteps.isEmpty()) {
                log.info("没有失败的步骤需要恢复，任务ID: {}", agentTaskId);
                return true;
            }

            // 重试所有失败的步骤
            boolean allRetrySuccessful = true;
            for (TaskStep step : failedSteps) {
                if (!retryStep(step.getId())) {
                    allRetrySuccessful = false;
                }
            }

            log.info("工作流恢复完成，任务ID: {}, 成功: {}", agentTaskId, allRetrySuccessful);
            return allRetrySuccessful;
        } catch (Exception e) {
            log.error("工作流恢复失败，任务ID: {}", agentTaskId, e);
            return false;
        }
    }

    /**
     * 内部步骤执行方法
     * Internal step execution method
     */
    private String executeStepInternal(Long agentTaskId, StepDefinition stepDef, AgentContext context) {
        log.debug("执行步骤内部逻辑，任务ID: {}, 步骤: {}", agentTaskId, stepDef.getName());

        TaskStep step = null;

        try {
            // 查找数据库中已存在的TaskStep记录（由TaskOrchestratorService创建）
            step = findExistingTaskStep(agentTaskId, stepDef);

            if (step == null) {
                // 如果没有找到，创建新的步骤记录
                step = taskStepService.createStep(agentTaskId, stepDef.getName(), stepDef.getType(), stepDef.getOrder());
            } else {
                // 更新步骤类型（如果数据库中为空）
                if (step.getStepType() == null && stepDef.getType() != null) {
                    step.setStepType(stepDef.getType());
                    taskStepService.updateStep(step);
                }
            }

            // 开始步骤执行
            taskStepService.startStep(step.getId());

            // 准备输入数据
            String input = prepareStepInput(context, stepDef);
            taskStepService.recordInput(step.getId(), input);

            // 准备步骤参数
            Map<String, Object> parameters = prepareStepParameters(stepDef, context);

            // 获取处理器并执行
            StepProcessor processor = processorFactory.getProcessor(stepDef.getType());
            String output = processor.process(input, parameters);

            // 完成步骤
            taskStepService.completeStep(step.getId(), output);

            // 存储结果到上下文
            if (stepDef.getOutputKey() != null) {
                contextService.storeVariable(context.getId(), stepDef.getOutputKey(), output);
            }

            log.debug("步骤执行成功，步骤ID: {}, 输出长度: {}", step.getId(), output != null ? output.length() : 0);

            return output;
        } catch (Exception e) {
            log.error("步骤执行失败，步骤定义: {}", stepDef.getName(), e);

            // 记录错误
            if (step != null) {
                taskStepService.failStep(step.getId(), e.getMessage());
            }

            // 根据策略决定是否重新抛出异常
            if (stepDef.isRequired()) {
                throw new RuntimeException("必要步骤执行失败: " + stepDef.getName(), e);
            }

            return null; // 非必要步骤失败时返回null
        }
    }

    /**
     * 查找数据库中已存在的TaskStep记录
     * <p>
     * 根据任务ID和步骤名称查找已经由TaskOrchestratorService创建的TaskStep记录，
     * 避免重复创建，确保状态更新能正确关联到数据库记录。
     *
     * @param agentTaskId 任务ID
     * @param stepDef 步骤定义
     * @return 找到的TaskStep记录，如果没有找到返回null
     */
    private TaskStep findExistingTaskStep(Long agentTaskId, StepDefinition stepDef) {
        try {
            // 根据任务ID获取所有步骤
            List<TaskStep> existingSteps = taskStepService.getStepsByTaskId(agentTaskId);

            // 根据步骤名称和顺序查找匹配的步骤
            return existingSteps.stream()
                .filter(step -> stepDef.getName().equals(step.getStepName()) ||
                               (stepDef.getOrder() != null && stepDef.getOrder().equals(step.getStepOrder())))
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("查找已存在的TaskStep失败，任务ID: {}, 步骤: {}", agentTaskId, stepDef.getName(), e);
            return null;
        }
    }

    /**
     * 准备步骤输入数据
     * Prepare step input data
     */
    private String prepareStepInput(AgentContext context, StepDefinition stepDef) {
        if (stepDef.getInputKeys() == null || stepDef.getInputKeys().isEmpty()) {
            return "";
        }

        // 从上下文获取输入数据
        List<String> inputValues = stepDef
            .getInputKeys()
            .stream()
            .map(key -> contextService.getVariable(context.getId(), key, String.class))
            .filter(value -> value != null)
            .collect(Collectors.toList());

        return String.join("\n\n", inputValues);
    }

    /**
     * 准备步骤参数
     * Prepare step parameters
     */
    private Map<String, Object> prepareStepParameters(StepDefinition stepDef, AgentContext context) {
        Map<String, Object> parameters = new HashMap<>();

        // 添加上下文相关参数
        if (stepDef.getParameters() != null) {
            parameters.putAll(stepDef.getParameters());
        }

        // 添加上下文相关参数
        parameters.put("tenantId", context.getAgentTask().getTenantId());
        parameters.put("agentTaskId", context.getAgentTask().getId());

        return parameters;
    }
}
