package com.whiskerguard.ai.service.agent.core.processor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * 步骤处理器工厂
 * Step Processor Factory
 *
 * 管理和提供不同类型的步骤处理器
 * Manage and provide different types of step processors
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StepProcessorFactory {

    private final Map<String, StepProcessor> processors = new HashMap<>();

    /**
     * 构造函数注入所有步骤处理器
     * Constructor injection of all step processors
     *
     * @param allProcessors 所有步骤处理器 / All step processors
     */
    public StepProcessorFactory(List<StepProcessor> allProcessors) {
        for (StepProcessor processor : allProcessors) {
            processors.put(processor.getType(), processor);
        }
    }

    /**
     * 获取指定类型的处理器
     * Get processor of specified type
     *
     * @param stepType 步骤类型 / Step type
     * @return 步骤处理器 / Step processor
     * @throws IllegalArgumentException 如果找不到处理器 / If processor not found
     */
    public StepProcessor getProcessor(String stepType) {
        StepProcessor processor = processors.get(stepType);
        if (processor == null) {
            throw new IllegalArgumentException("No processor found for step type: " + stepType);
        }
        return processor;
    }

    /**
     * 检查是否存在指定类型的处理器
     * Check if processor exists for specified type
     *
     * @param stepType 步骤类型 / Step type
     * @return 是否存在 / Whether exists
     */
    public boolean hasProcessor(String stepType) {
        return processors.containsKey(stepType);
    }

    /**
     * 获取所有已注册的处理器类型
     * Get all registered processor types
     *
     * @return 处理器类型集合 / Set of processor types
     */
    public Map<String, StepProcessor> getAllProcessors() {
        return new HashMap<>(processors);
    }

    /**
     * 注册新的处理器
     * Register new processor
     *
     * @param processor 步骤处理器 / Step processor
     */
    public void registerProcessor(StepProcessor processor) {
        processors.put(processor.getType(), processor);
    }

    /**
     * 移除处理器
     * Remove processor
     *
     * @param stepType 步骤类型 / Step type
     * @return 被移除的处理器 / Removed processor
     */
    public StepProcessor removeProcessor(String stepType) {
        return processors.remove(stepType);
    }
}
