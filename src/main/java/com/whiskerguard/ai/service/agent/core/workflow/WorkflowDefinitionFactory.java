package com.whiskerguard.ai.service.agent.core.workflow;

import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import java.util.List;
import java.util.Map;

/**
 * 工作流定义工厂
 * Workflow Definition Factory
 *
 * 提供不同业务类型的标准工作流定义
 * Provide standard workflow definitions for different business types
 *
 * <AUTHOR>
 * @since 1.0
 */
public class WorkflowDefinitionFactory {

    /**
     * 获取合同审查工作流
     * Get contract review workflow
     */
    public static List<StepDefinition> getContractReviewWorkflow() {
        return List.of(
            new StepDefinition("retrievePartyInfo", "RETRIEVAL", 1)
                .withParameters(Map.of("type", "PARTY_INFORMATION"))
                .withOutputKey("partyInfo")
                .withRequired(true),
            new StepDefinition("retrieveLegalRegulations", "RETRIEVAL", 2)
                .withParameters(Map.of("type", "LEGAL_REFERENCE"))
                .withOutputKey("legalRegulations")
                .withRequired(true),
            new StepDefinition("analyzeParties", "ANALYSIS", 3)
                .withInputKeys(List.of("partyInfo"))
                .withParameters(Map.of("analysisType", "party"))
                .withOutputKey("partyAnalysis")
                .withRequired(true),
            new StepDefinition("analyzeContractClauses", "ANALYSIS", 4)
                .withInputKeys(List.of("contractContent"))
                .withParameters(Map.of("analysisType", "clause"))
                .withOutputKey("clauseAnalysis")
                .withRequired(true),
            new StepDefinition("checkLegalCompliance", "ANALYSIS", 5)
                .withInputKeys(List.of("contractContent", "legalRegulations"))
                .withParameters(Map.of("analysisType", "compliance"))
                .withOutputKey("legalComplianceCheck")
                .withRequired(true),
            new StepDefinition("assessRisks", "ANALYSIS", 6)
                .withInputKeys(List.of("clauseAnalysis", "legalComplianceCheck", "partyAnalysis"))
                .withParameters(Map.of("analysisType", "risk"))
                .withOutputKey("riskAssessment")
                .withRequired(true),
            new StepDefinition("generateRecommendations", "LLM", 7)
                .withInputKeys(List.of("partyAnalysis", "clauseAnalysis", "legalComplianceCheck", "riskAssessment"))
                .withParameters(Map.of("promptTemplate", "基于以下分析结果，生成合同审查建议：\n\n%s\n\n请提供详细的审查建议和改进意见。"))
                .withOutputKey("reviewRecommendations")
                .withRequired(true)
        );
    }

    /**
     * 获取外规内化工作流
     * Get regulation internalization workflow
     */
    public static List<StepDefinition> getRegulationInternalizationWorkflow() {
        return List.of(
            new StepDefinition("retrieveRegulation", "RETRIEVAL", 1)
                .withParameters(Map.of("type", "REGULATION"))
                .withOutputKey("regulationContent")
                .withRequired(true),
            new StepDefinition("retrieveIndustryPractices", "RETRIEVAL", 2)
                .withParameters(Map.of("type", "INDUSTRY_PRACTICE"))
                .withOutputKey("industryPractices")
                .withRequired(true),
            new StepDefinition("analyzeRegulation", "ANALYSIS", 3)
                .withInputKeys(List.of("regulationContent"))
                .withParameters(Map.of("analysisType", "regulation"))
                .withOutputKey("regulationAnalysis")
                .withRequired(true),
            new StepDefinition("generateInternalPolicy", "LLM", 4)
                .withInputKeys(List.of("regulationContent", "regulationAnalysis", "industryPractices"))
                .withParameters(
                    Map.of("promptTemplate", "基于以下法规内容和分析结果，生成企业内部制度：\n\n%s\n\n请生成详细的内部制度草案。")
                )
                .withOutputKey("draftPolicy")
                .withRequired(true),
            new StepDefinition("validateCompliance", "ANALYSIS", 5)
                .withInputKeys(List.of("draftPolicy", "regulationContent"))
                .withParameters(Map.of("analysisType", "compliance"))
                .withOutputKey("validationResult")
                .withRequired(true)
        );
    }

    /**
     * 获取制度审查工作流
     * Get policy review workflow
     */
    public static List<StepDefinition> getPolicyReviewWorkflow() {
        return List.of(
            new StepDefinition("retrieveRegulations", "RETRIEVAL", 1)
                .withParameters(Map.of("type", "REGULATION"))
                .withOutputKey("regulationStandards")
                .withRequired(true),
            new StepDefinition("retrieveIndustryPractices", "RETRIEVAL", 2)
                .withParameters(Map.of("type", "INDUSTRY_PRACTICE"))
                .withOutputKey("industryPractices")
                .withRequired(false),
            new StepDefinition("analyzeStructure", "ANALYSIS", 3)
                .withInputKeys(List.of("policyContent"))
                .withParameters(Map.of("analysisType", "structure"))
                .withOutputKey("contentAnalysis")
                .withRequired(true),
            new StepDefinition("checkCompliance", "ANALYSIS", 4)
                .withInputKeys(List.of("policyContent", "regulationStandards"))
                .withParameters(Map.of("analysisType", "compliance"))
                .withOutputKey("complianceCheck")
                .withRequired(true),
            new StepDefinition("identifyRisks", "ANALYSIS", 5)
                .withInputKeys(List.of("policyContent", "regulationStandards"))
                .withParameters(Map.of("analysisType", "risk"))
                .withOutputKey("riskIdentification")
                .withRequired(true),
            new StepDefinition("generateSuggestions", "LLM", 6)
                .withInputKeys(List.of("contentAnalysis", "complianceCheck", "riskIdentification", "industryPractices"))
                .withParameters(Map.of("promptTemplate", "基于以下分析结果，生成制度优化建议：\n\n%s\n\n请提供详细的优化建议。"))
                .withOutputKey("optimizationSuggestions")
                .withRequired(true)
        );
    }
}
