package com.whiskerguard.ai.service.agent.core.impl;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.agent.core.TaskStepCoreService;
import jakarta.persistence.EntityNotFoundException;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 任务步骤核心服务实现
 * Task Step Core Service Implementation
 *
 * 提供Agent任务步骤的管理和执行功能实现
 * Provide implementation of management and execution functions for Agent task steps
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class TaskStepCoreServiceImpl implements TaskStepCoreService {

    private static final Logger log = LoggerFactory.getLogger(TaskStepCoreServiceImpl.class);

    private final TaskStepRepository taskStepRepository;
    private final AgentTaskRepository agentTaskRepository;

    /**
     * 构造函数注入依赖
     * Constructor dependency injection
     */
    public TaskStepCoreServiceImpl(TaskStepRepository taskStepRepository, AgentTaskRepository agentTaskRepository) {
        this.taskStepRepository = taskStepRepository;
        this.agentTaskRepository = agentTaskRepository;
    }

    @Override
    public TaskStep createStep(Long agentTaskId, String stepName, String stepType, Integer stepOrder) {
        return createStep(agentTaskId, stepName, null, stepType, stepOrder);
    }

    @Override
    public TaskStep createStep(Long agentTaskId, String stepName, String stepDescription, String stepType, Integer stepOrder) {
        log.debug(
            "创建任务步骤 - 任务ID: {}, 步骤名称: {}, 类型: {}, 顺序: {} / Creating task step - Task ID: {}, Step name: {}, Type: {}, Order: {}",
            agentTaskId,
            stepName,
            stepType,
            stepOrder,
            agentTaskId,
            stepName,
            stepType,
            stepOrder
        );

        // 获取关联的AgentTask / Get associated AgentTask
        AgentTask agentTask = agentTaskRepository
            .findById(agentTaskId)
            .orElseThrow(() -> {
                log.error("未找到Agent任务 - ID: {} / Agent task not found - ID: {}", agentTaskId, agentTaskId);
                return new EntityNotFoundException("Agent任务不存在 - ID: " + agentTaskId + " / Agent task not found - ID: " + agentTaskId);
            });

        // 创建新的步骤实体 / Create new step entity
        TaskStep step = new TaskStep();
        step.setAgentTask(agentTask); // 设置任务关联
        step.setStepName(stepName);
        step.setStepDescription(stepDescription);
        step.setStepType(stepType);
        step.setStepOrder(stepOrder);
        step.setStatus(TaskStepStatus.PENDING);
        step.setRetryCount(0);
        step.setVersion(1);
        step.setIsDeleted(false);

        // 设置审计字段 / Set audit fields
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        Instant now = Instant.now();
        step.setCreatedBy(currentUser);
        step.setCreatedAt(now);
        step.setUpdatedBy(currentUser);
        step.setUpdatedAt(now);

        // 保存并返回 / Save and return
        TaskStep savedStep = taskStepRepository.save(step);
        log.info(
            "成功创建任务步骤 - ID: {}, 任务ID: {}, 步骤名称: {} / Successfully created task step - ID: {}, Task ID: {}, Step name: {}",
            savedStep.getId(),
            agentTaskId,
            stepName,
            savedStep.getId(),
            agentTaskId,
            stepName
        );

        return savedStep;
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void updateStepStatus(Long stepId, TaskStepStatus status) {
        log.debug(
            "更新步骤状态 - 步骤ID: {}, 新状态: {} / Updating step status - Step ID: {}, New status: {}",
            stepId,
            status,
            stepId,
            status
        );

        TaskStep step = getStepById(stepId);
        TaskStepStatus oldStatus = step.getStatus();
        step.setStatus(status);

        // 如果状态改为RUNNING，记录开始时间 / If status changes to RUNNING, record start time
        if (status == TaskStepStatus.RUNNING && step.getStartTime() == null) {
            step.setStartTime(Instant.now());
        }

        // 如果状态改为完成状态，记录结束时间和执行时长 / If status changes to completion status, record end time and execution time
        if (isCompletionStatus(status) && step.getEndTime() == null) {
            Instant endTime = Instant.now();
            step.setEndTime(endTime);

            if (step.getStartTime() != null) {
                long executionTime = Duration.between(step.getStartTime(), endTime).toMillis();
                step.setExecutionTime(executionTime);
            }
        }

        updateAuditFields(step);
        taskStepRepository.save(step);

        log.info(
            "步骤状态已更新 - 步骤ID: {}, 从 {} 变为 {} / Step status updated - Step ID: {}, from {} to {}",
            stepId,
            oldStatus,
            status,
            stepId,
            oldStatus,
            status
        );
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void recordInput(Long stepId, String inputData) {
        log.debug("记录步骤输入 - 步骤ID: {} / Recording step input - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setInputData(inputData);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void recordOutput(Long stepId, String outputData) {
        log.debug("记录步骤输出 - 步骤ID: {} / Recording step output - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setOutputData(outputData);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void recordError(Long stepId, String errorMessage) {
        log.debug("记录步骤错误 - 步骤ID: {} / Recording step error - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setErrorMessage(errorMessage);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    public void startStep(Long stepId) {
        log.debug("开始执行步骤 - 步骤ID: {} / Starting step execution - Step ID: {}", stepId, stepId);
        updateStepStatus(stepId, TaskStepStatus.RUNNING);
    }

    @Override
    public void completeStep(Long stepId, String outputData) {
        log.debug("完成步骤执行 - 步骤ID: {} / Completing step execution - Step ID: {}", stepId, stepId);

        if (outputData != null) {
            recordOutput(stepId, outputData);
        }
        updateStepStatus(stepId, TaskStepStatus.COMPLETED);
    }

    @Override
    public void failStep(Long stepId, String errorMessage) {
        log.debug("步骤执行失败 - 步骤ID: {} / Step execution failed - Step ID: {}", stepId, stepId);

        if (errorMessage != null) {
            recordError(stepId, errorMessage);
        }
        updateStepStatus(stepId, TaskStepStatus.FAILED);
    }

    @Override
    public void skipStep(Long stepId, String reason) {
        log.debug(
            "跳过步骤执行 - 步骤ID: {}, 原因: {} / Skipping step execution - Step ID: {}, Reason: {}",
            stepId,
            reason,
            stepId,
            reason
        );

        if (reason != null) {
            // 将跳过原因记录在错误信息字段中 / Record skip reason in error message field
            recordError(stepId, "步骤被跳过: " + reason + " / Step skipped: " + reason);
        }
        updateStepStatus(stepId, TaskStepStatus.SKIPPED);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public boolean retryStep(Long stepId) {
        log.debug("重试步骤执行 - 步骤ID: {} / Retrying step execution - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);

        // 检查是否可以重试 / Check if can retry
        if (step.getStatus() != TaskStepStatus.FAILED) {
            log.warn(
                "只有失败的步骤可以重试 - 步骤ID: {}, 当前状态: {} / Only failed steps can be retried - Step ID: {}, Current status: {}",
                stepId,
                step.getStatus(),
                stepId,
                step.getStatus()
            );
            return false;
        }

        // 增加重试次数 / Increment retry count
        Integer retryCount = step.getRetryCount() != null ? step.getRetryCount() + 1 : 1;
        step.setRetryCount(retryCount);

        // 清除之前的错误信息和结束时间 / Clear previous error message and end time
        step.setErrorMessage(null);
        step.setEndTime(null);
        step.setExecutionTime(null);

        // 重置状态为PENDING / Reset status to PENDING
        step.setStatus(TaskStepStatus.PENDING);

        updateAuditFields(step);
        taskStepRepository.save(step);

        log.info(
            "步骤重试已设置 - 步骤ID: {}, 重试次数: {} / Step retry set - Step ID: {}, Retry count: {}",
            stepId,
            retryCount,
            stepId,
            retryCount
        );

        return true;
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "task-steps", key = "#agentTaskId")
    public List<TaskStep> getTaskSteps(Long agentTaskId) {
        log.debug("获取任务步骤 - 任务ID: {} / Getting task steps - Task ID: {}", agentTaskId, agentTaskId);
        return taskStepRepository.findByAgentTaskIdAndIsDeletedFalse(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getTaskStepsOrdered(Long agentTaskId) {
        log.debug("获取任务步骤（排序）- 任务ID: {} / Getting task steps (ordered) - Task ID: {}", agentTaskId, agentTaskId);
        return taskStepRepository.findByAgentTaskIdAndIsDeletedFalseOrderByStepOrder(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByStatus(Long agentTaskId, TaskStepStatus status) {
        log.debug(
            "获取指定状态的步骤 - 任务ID: {}, 状态: {} / Getting steps by status - Task ID: {}, Status: {}",
            agentTaskId,
            status,
            agentTaskId,
            status
        );
        return taskStepRepository.findByAgentTaskIdAndStatusAndIsDeletedFalse(agentTaskId, status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getExecutableSteps(Long agentTaskId) {
        log.debug("获取可执行步骤 - 任务ID: {} / Getting executable steps - Task ID: {}", agentTaskId, agentTaskId);

        List<TaskStep> pendingSteps = getStepsByStatus(agentTaskId, TaskStepStatus.PENDING);

        return pendingSteps.stream().filter(step -> canExecuteStep(step.getId())).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canExecuteStep(Long stepId) {
        log.debug("检查步骤是否可执行 - 步骤ID: {} / Checking if step can execute - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);

        // 如果状态不是PENDING，则不能执行 / If status is not PENDING, cannot execute
        if (step.getStatus() != TaskStepStatus.PENDING) {
            return false;
        }

        // TODO: 这里可以实现更复杂的依赖检查逻辑
        // TODO: Here you can implement more complex dependency checking logic
        // 目前简单返回true，后续可以根据dependsOn字段检查依赖
        // Currently simply return true, can check dependencies based on dependsOn field later

        return true;
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void updateStepMetadata(Long stepId, String metadata) {
        log.debug("更新步骤元数据 - 步骤ID: {} / Updating step metadata - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setMetadata(metadata);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @Transactional(readOnly = true)
    public int getTaskProgress(Long agentTaskId) {
        log.debug("计算任务进度 - 任务ID: {} / Calculating task progress - Task ID: {}", agentTaskId, agentTaskId);

        List<TaskStep> allSteps = getTaskSteps(agentTaskId);
        if (allSteps.isEmpty()) {
            return 0;
        }

        long completedSteps = allSteps.stream().filter(step -> isCompletionStatus(step.getStatus())).count();

        int progress = (int) ((completedSteps * 100) / allSteps.size());
        log.debug(
            "任务进度计算完成 - 任务ID: {}, 进度: {}% / Task progress calculated - Task ID: {}, Progress: {}%",
            agentTaskId,
            progress,
            agentTaskId,
            progress
        );

        return progress;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getTaskStepStatistics(Long agentTaskId) {
        log.debug("获取任务步骤统计 - 任务ID: {} / Getting task step statistics - Task ID: {}", agentTaskId, agentTaskId);

        List<TaskStep> allSteps = getTaskSteps(agentTaskId);
        Map<String, Object> statistics = new HashMap<>();

        // 基本统计 / Basic statistics
        statistics.put("totalSteps", allSteps.size());
        statistics.put("completedSteps", allSteps.stream().filter(s -> s.getStatus() == TaskStepStatus.COMPLETED).count());
        statistics.put("failedSteps", allSteps.stream().filter(s -> s.getStatus() == TaskStepStatus.FAILED).count());
        statistics.put("runningSteps", allSteps.stream().filter(s -> s.getStatus() == TaskStepStatus.RUNNING).count());
        statistics.put("pendingSteps", allSteps.stream().filter(s -> s.getStatus() == TaskStepStatus.PENDING).count());
        statistics.put("skippedSteps", allSteps.stream().filter(s -> s.getStatus() == TaskStepStatus.SKIPPED).count());

        // 执行时间统计 / Execution time statistics
        List<Long> executionTimes = allSteps
            .stream()
            .filter(s -> s.getExecutionTime() != null)
            .map(TaskStep::getExecutionTime)
            .collect(Collectors.toList());

        if (!executionTimes.isEmpty()) {
            statistics.put("totalExecutionTime", executionTimes.stream().mapToLong(Long::longValue).sum());
            statistics.put("averageExecutionTime", executionTimes.stream().mapToLong(Long::longValue).average().orElse(0.0));
            statistics.put("maxExecutionTime", executionTimes.stream().mapToLong(Long::longValue).max().orElse(0L));
            statistics.put("minExecutionTime", executionTimes.stream().mapToLong(Long::longValue).min().orElse(0L));
        }

        // 重试统计 / Retry statistics
        int totalRetries = allSteps.stream().mapToInt(s -> s.getRetryCount() != null ? s.getRetryCount() : 0).sum();
        statistics.put("totalRetries", totalRetries);

        log.debug("任务步骤统计完成 - 任务ID: {} / Task step statistics completed - Task ID: {}", agentTaskId, agentTaskId);
        return statistics;
    }

    // 添加新接口方法的实现 / Add implementations for new interface methods

    @Override
    @Transactional(readOnly = true)
    public TaskStep getStep(Long stepId) {
        log.debug("获取单个步骤 - 步骤ID: {} / Getting single step - Step ID: {}", stepId, stepId);
        return getStepById(stepId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByTask(Long agentTaskId) {
        return getTaskSteps(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByStatusAndTask(Long agentTaskId, TaskStepStatus status) {
        return getStepsByStatus(agentTaskId, status);
    }

    @Override
    public void setInput(Long stepId, String inputData) {
        recordInput(stepId, inputData);
    }

    @Override
    public void updateStatus(Long stepId, TaskStepStatus status) {
        updateStepStatus(stepId, status);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void incrementRetryCount(Long stepId) {
        log.debug("增加重试计数 - 步骤ID: {} / Incrementing retry count - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        Integer currentRetryCount = step.getRetryCount() != null ? step.getRetryCount() : 0;
        step.setRetryCount(currentRetryCount + 1);

        updateAuditFields(step);
        taskStepRepository.save(step);

        log.debug(
            "重试计数已增加 - 步骤ID: {}, 新计数: {} / Retry count incremented - Step ID: {}, New count: {}",
            stepId,
            step.getRetryCount(),
            stepId,
            step.getRetryCount()
        );
    }

    /**
     * 根据ID获取步骤实体
     * Get step entity by ID
     */
    private TaskStep getStepById(Long stepId) {
        return taskStepRepository
            .findById(stepId)
            .orElseThrow(() -> {
                log.error("未找到步骤 - ID: {} / Step not found - ID: {}", stepId, stepId);
                return new EntityNotFoundException("步骤不存在 - ID: " + stepId + " / Step not found - ID: " + stepId);
            });
    }

    /**
     * 检查是否为完成状态
     * Check if it's a completion status
     */
    private boolean isCompletionStatus(TaskStepStatus status) {
        return status == TaskStepStatus.COMPLETED || status == TaskStepStatus.FAILED || status == TaskStepStatus.SKIPPED;
    }

    /**
     * 更新审计字段
     * Update audit fields
     */
    private void updateAuditFields(TaskStep step) {
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        step.setUpdatedBy(currentUser);
        step.setUpdatedAt(Instant.now());
        step.setVersion(step.getVersion() + 1);
    }
}
