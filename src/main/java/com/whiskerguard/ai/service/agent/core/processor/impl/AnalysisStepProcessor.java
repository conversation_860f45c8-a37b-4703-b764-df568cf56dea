package com.whiskerguard.ai.service.agent.core.processor.impl;

import com.whiskerguard.ai.service.agent.core.processor.StepProcessor;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 分析步骤处理器
 * Analysis Step Processor
 *
 * 处理分析类型的步骤，通常包装LLM调用
 * Process analysis type steps, usually wrapping LLM calls
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class AnalysisStepProcessor implements StepProcessor {

    private static final Logger log = LoggerFactory.getLogger(AnalysisStepProcessor.class);

    private final StepProcessor llmProcessor;

    public AnalysisStepProcessor(LlmStepProcessor llmProcessor) {
        this.llmProcessor = llmProcessor;
    }

    @Override
    public String getType() {
        return "ANALYSIS";
    }

    @Override
    public String process(String input, Map<String, Object> parameters) {
        log.debug("开始处理分析步骤，输入长度: {}, 参数: {}", input != null ? input.length() : 0, parameters);

        try {
            // 构建带有分析指令的提示词
            String analysisPrompt = buildAnalysisPrompt(input, parameters);

            // 更新参数，设置分析提示词
            parameters.put("promptTemplate", analysisPrompt);

            // 调用LLM处理器
            String result = llmProcessor.process(input, parameters);

            log.debug("分析步骤处理完成，结果长度: {}", result != null ? result.length() : 0);
            return result;
        } catch (Exception e) {
            log.error("分析步骤处理失败", e);
            throw new RuntimeException("分析步骤处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateParameters(Map<String, Object> parameters) {
        // 分析步骤通常需要分析类型参数
        return parameters.containsKey("analysisType") || parameters.containsKey("promptTemplate");
    }

    @Override
    public String getDescription() {
        return "分析步骤处理器，提供各种类型的智能分析功能";
    }

    /**
     * 构建分析提示词
     * Build analysis prompt
     *
     * @param input 原始输入 / Original input
     * @param parameters 步骤参数 / Step parameters
     * @return 分析提示词 / Analysis prompt
     */
    private String buildAnalysisPrompt(String input, Map<String, Object> parameters) {
        // 如果已有自定义提示词模板，直接使用
        String customTemplate = (String) parameters.get("promptTemplate");
        if (customTemplate != null && !customTemplate.trim().isEmpty()) {
            return customTemplate;
        }

        // 根据分析类型生成默认提示词
        String analysisType = (String) parameters.get("analysisType");
        if (analysisType == null) {
            analysisType = "general";
        }

        return switch (analysisType.toLowerCase()) {
            case "structure" -> "请分析以下内容的结构和组织方式，包括主要部分、层次关系和逻辑结构：\n\n%s\n\n请提供详细的结构分析结果。";
            case "compliance" -> "请对以下内容进行合规性分析，识别可能的合规风险和问题：\n\n%s\n\n请提供详细的合规性分析报告。";
            case "risk" -> "请对以下内容进行风险分析，识别潜在风险点和风险级别：\n\n%s\n\n请提供详细的风险分析报告。";
            case "clause" -> "请对以下合同条款进行详细分析，包括条款的合理性、风险点和改进建议：\n\n%s\n\n请提供详细的条款分析结果。";
            case "party" -> "请对以下关联方信息进行分析，包括信用状况、合作历史和风险评估：\n\n%s\n\n请提供详细的关联方分析报告。";
            case "regulation" -> "请对以下法规内容进行分析，提取关键要求和实施要点：\n\n%s\n\n请提供详细的法规分析结果。";
            default -> "请对以下内容进行综合分析，包括关键信息提取、问题识别和建议：\n\n%s\n\n请提供详细的分析结果。";
        };
    }
}
