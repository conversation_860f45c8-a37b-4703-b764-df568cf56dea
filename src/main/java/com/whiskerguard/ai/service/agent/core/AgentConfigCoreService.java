package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.domain.AgentConfig;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Agent配置核心服务接口
 * Agent Configuration Core Service Interface
 *
 * 提供Agent配置的管理和查询功能
 * Provide management and query functions for Agent configurations
 *
 * 主要功能：
 * - 分层配置管理（全局 -> 租户 -> 任务类型）
 * - 配置优先级和覆盖机制
 * - 类型安全的配置值获取
 * - 配置缓存和性能优化
 *
 * Main functions:
 * - Hierarchical configuration management (global -> tenant -> task type)
 * - Configuration priority and override mechanism
 * - Type-safe configuration value retrieval
 * - Configuration caching and performance optimization
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface AgentConfigCoreService {
    /**
     * 获取配置值（支持类型转换）
     * Get configuration value (with type conversion)
     *
     * @param key 配置键 / Configuration key
     * @param valueType 值类型 / Value type
     * @param tenantId 租户ID（可为null） / Tenant ID (nullable)
     * @param taskType 任务类型（可为null） / Task type (nullable)
     * @param <T> 返回值类型 / Return value type
     * @return 配置值 / Configuration value
     */
    <T> Optional<T> getConfig(String key, Class<T> valueType, Long tenantId, AgentTaskType taskType);

    /**
     * 获取配置值（带默认值）
     * Get configuration value (with default value)
     *
     * @param key 配置键 / Configuration key
     * @param defaultValue 默认值 / Default value
     * @param tenantId 租户ID（可为null） / Tenant ID (nullable)
     * @param taskType 任务类型（可为null） / Task type (nullable)
     * @param <T> 值类型 / Value type
     * @return 配置值或默认值 / Configuration value or default value
     */
    <T> T getConfig(String key, T defaultValue, Long tenantId, AgentTaskType taskType);

    /**
     * 获取字符串配置值
     * Get string configuration value
     *
     * @param key 配置键 / Configuration key
     * @param tenantId 租户ID（可为null） / Tenant ID (nullable)
     * @param taskType 任务类型（可为null） / Task type (nullable)
     * @return 配置值 / Configuration value
     */
    Optional<String> getStringConfig(String key, Long tenantId, AgentTaskType taskType);

    /**
     * 获取整数配置值
     * Get integer configuration value
     *
     * @param key 配置键 / Configuration key
     * @param tenantId 租户ID（可为null） / Tenant ID (nullable)
     * @param taskType 任务类型（可为null） / Task type (nullable)
     * @return 配置值 / Configuration value
     */
    Optional<Integer> getIntegerConfig(String key, Long tenantId, AgentTaskType taskType);

    /**
     * 获取布尔配置值
     * Get boolean configuration value
     *
     * @param key 配置键 / Configuration key
     * @param tenantId 租户ID（可为null） / Tenant ID (nullable)
     * @param taskType 任务类型（可为null） / Task type (nullable)
     * @return 配置值 / Configuration value
     */
    Optional<Boolean> getBooleanConfig(String key, Long tenantId, AgentTaskType taskType);

    /**
     * 获取双精度配置值
     * Get double configuration value
     *
     * @param key 配置键 / Configuration key
     * @param tenantId 租户ID（可为null） / Tenant ID (nullable)
     * @param taskType 任务类型（可为null） / Task type (nullable)
     * @return 配置值 / Configuration value
     */
    Optional<Double> getDoubleConfig(String key, Long tenantId, AgentTaskType taskType);

    /**
     * 设置或更新配置
     * Set or update configuration
     *
     * @param key 配置键 / Configuration key
     * @param value 配置值 / Configuration value
     * @param tenantId 租户ID（可为null表示全局配置） / Tenant ID (null for global config)
     * @param taskType 任务类型（可为null表示适用所有任务类型） / Task type (null for all task types)
     * @param configType 配置类型 / Configuration type
     * @param configGroup 配置分组 / Configuration group
     * @param description 描述 / Description
     * @param priority 优先级 / Priority
     * @return 保存的配置实体 / Saved configuration entity
     */
    AgentConfig setConfig(
        String key,
        Object value,
        Long tenantId,
        AgentTaskType taskType,
        String configType,
        String configGroup,
        String description,
        Integer priority
    );

    /**
     * 设置配置（简化版本）
     * Set configuration (simplified version)
     *
     * @param key 配置键 / Configuration key
     * @param value 配置值 / Configuration value
     * @param tenantId 租户ID / Tenant ID
     * @param taskType 任务类型 / Task type
     * @return 保存的配置实体 / Saved configuration entity
     */
    AgentConfig setConfig(String key, Object value, Long tenantId, AgentTaskType taskType);

    /**
     * 检查配置是否存在
     * Check if configuration exists
     *
     * @param key 配置键 / Configuration key
     * @param tenantId 租户ID / Tenant ID
     * @param taskType 任务类型 / Task type
     * @return 是否存在 / Whether exists
     */
    boolean hasConfig(String key, Long tenantId, AgentTaskType taskType);

    /**
     * 删除配置
     * Delete configuration
     *
     * @param key 配置键 / Configuration key
     * @param tenantId 租户ID / Tenant ID
     * @param taskType 任务类型 / Task type
     * @return 是否删除成功 / Whether deletion was successful
     */
    boolean removeConfig(String key, Long tenantId, AgentTaskType taskType);

    /**
     * 获取租户的所有配置
     * Get all configurations for a tenant
     *
     * @param tenantId 租户ID / Tenant ID
     * @return 配置列表 / Configuration list
     */
    List<AgentConfig> getTenantConfigs(Long tenantId);

    /**
     * 根据配置组获取配置
     * Get configurations by config group
     *
     * @param configGroup 配置分组 / Configuration group
     * @param tenantId 租户ID / Tenant ID
     * @return 配置列表 / Configuration list
     */
    List<AgentConfig> getConfigsByGroup(String configGroup, Long tenantId);

    /**
     * 根据配置类型获取配置
     * Get configurations by config type
     *
     * @param configType 配置类型 / Configuration type
     * @param tenantId 租户ID / Tenant ID
     * @return 配置列表 / Configuration list
     */
    List<AgentConfig> getConfigsByType(String configType, Long tenantId);

    /**
     * 批量获取配置值
     * Batch get configuration values
     *
     * @param keys 配置键列表 / Configuration key list
     * @param tenantId 租户ID / Tenant ID
     * @param taskType 任务类型 / Task type
     * @return 配置映射 / Configuration map
     */
    Map<String, Object> getBatchConfigs(List<String> keys, Long tenantId, AgentTaskType taskType);

    /**
     * 启用配置
     * Enable configuration
     *
     * @param configId 配置ID / Configuration ID
     * @return 是否成功 / Whether successful
     */
    boolean enableConfig(Long configId);

    /**
     * 禁用配置
     * Disable configuration
     *
     * @param configId 配置ID / Configuration ID
     * @return 是否成功 / Whether successful
     */
    boolean disableConfig(Long configId);

    /**
     * 更新配置优先级
     * Update configuration priority
     *
     * @param configId 配置ID / Configuration ID
     * @param priority 新优先级 / New priority
     * @return 是否成功 / Whether successful
     */
    boolean updateConfigPriority(Long configId, Integer priority);

    /**
     * 清除配置缓存
     * Clear configuration cache
     *
     * @param tenantId 租户ID（可为null表示清除所有） / Tenant ID (null to clear all)
     */
    void clearConfigCache(Long tenantId);
}
