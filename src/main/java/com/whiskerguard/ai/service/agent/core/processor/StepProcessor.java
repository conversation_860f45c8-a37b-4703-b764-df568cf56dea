package com.whiskerguard.ai.service.agent.core.processor;

import java.util.Map;

/**
 * 步骤处理器接口
 * Step Processor Interface
 *
 * 定义不同类型步骤的处理逻辑接口
 * Define processing logic interface for different types of steps
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface StepProcessor {
    /**
     * 获取处理器类型
     * Get processor type
     *
     * @return 处理器类型 / Processor type
     */
    String getType();

    /**
     * 处理步骤
     * Process step
     *
     * @param input 输入数据 / Input data
     * @param parameters 步骤参数 / Step parameters
     * @return 处理结果 / Processing result
     */
    String process(String input, Map<String, Object> parameters);

    /**
     * 检查是否支持此步骤类型
     * Check if this step type is supported
     *
     * @param stepType 步骤类型 / Step type
     * @return 是否支持 / Whether supported
     */
    default boolean supports(String stepType) {
        return getType().equals(stepType);
    }

    /**
     * 获取处理器描述
     * Get processor description
     *
     * @return 描述信息 / Description
     */
    default String getDescription() {
        return "Step processor for type: " + getType();
    }

    /**
     * 验证参数
     * Validate parameters
     *
     * @param parameters 参数映射 / Parameter map
     * @return 是否有效 / Whether valid
     */
    default boolean validateParameters(Map<String, Object> parameters) {
        return true; // Default implementation accepts all parameters
    }
}
