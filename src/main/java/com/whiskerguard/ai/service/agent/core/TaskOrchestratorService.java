/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：TaskOrchestratorService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：任务编排服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.repository.TaskStepRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 任务编排服务
 * <p>
 * 负责Agent任务的步骤编排和执行协调。
 * 根据不同的任务类型创建相应的执行步骤，并管理步骤的执行状态。
 *
 * 主要功能：
 * 1. 任务步骤规划
 * 2. 执行流程编排
 * 3. 步骤状态管理
 * 4. 执行进度跟踪
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class TaskOrchestratorService {

    private static final Logger log = LoggerFactory.getLogger(TaskOrchestratorService.class);

    private final TaskStepRepository taskStepRepository;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public TaskOrchestratorService(TaskStepRepository taskStepRepository) {
        this.taskStepRepository = taskStepRepository;
    }

    /**
     * 为Agent任务创建执行步骤
     *
     * @param agentTask 任务实体
     * @return 创建的步骤列表
     */
    public List<TaskStep> createTaskSteps(AgentTask agentTask) {
        log.info("为任务创建执行步骤，任务ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

        List<TaskStep> steps = new ArrayList<>();

        switch (agentTask.getTaskType()) {
            case "REGULATION_INTERNALIZATION" -> steps = createRegulationInternalizationSteps(agentTask);
            case "POLICY_REVIEW" -> steps = createPolicyReviewSteps(agentTask);
            case "CONTRACT_REVIEW" -> steps = createContractReviewSteps(agentTask);
            default -> throw new IllegalArgumentException("不支持的任务类型: " + agentTask.getTaskType());
        }

        // 保存步骤到数据库
        steps = taskStepRepository.saveAll(steps);

        log.info("任务步骤创建完成，任务ID: {}, 步骤数量: {}", agentTask.getId(), steps.size());
        return steps;
    }

    /**
     * 创建外规内化任务步骤
     */
    private List<TaskStep> createRegulationInternalizationSteps(AgentTask agentTask) {
        List<TaskStep> steps = new ArrayList<>();

        steps.add(createTaskStep(agentTask, 1, "法规内容检索", "从RAG服务检索相关法规内容"));
        steps.add(createTaskStep(agentTask, 2, "行业实践检索", "检索行业最佳实践案例"));
        steps.add(createTaskStep(agentTask, 3, "企业制度检索", "检索企业现有相关制度"));
        steps.add(createTaskStep(agentTask, 4, "内部制度生成", "基于法规和企业特点生成内部制度"));
        steps.add(createTaskStep(agentTask, 5, "合规性验证", "验证生成制度的合规性"));
        steps.add(createTaskStep(agentTask, 6, "结果整合", "整合所有结果并生成最终响应"));

        return steps;
    }

    /**
     * 创建制度审查任务步骤
     */
    private List<TaskStep> createPolicyReviewSteps(AgentTask agentTask) {
        List<TaskStep> steps = new ArrayList<>();

        steps.add(createTaskStep(agentTask, 1, "法规标准检索", "检索相关法规标准"));
        steps.add(createTaskStep(agentTask, 2, "行业实践检索", "检索行业最佳实践"));
        steps.add(createTaskStep(agentTask, 3, "历史制度检索", "检索企业历史制度"));
        steps.add(createTaskStep(agentTask, 4, "内容结构分析", "分析制度内容和结构"));
        steps.add(createTaskStep(agentTask, 5, "合规性检查", "检查制度合规性"));
        steps.add(createTaskStep(agentTask, 6, "风险点识别", "识别潜在风险点"));
        steps.add(createTaskStep(agentTask, 7, "优化建议生成", "生成优化建议"));
        steps.add(createTaskStep(agentTask, 8, "结果整合", "整合审查结果"));

        return steps;
    }

    /**
     * 创建合同审查任务步骤
     */
    private List<TaskStep> createContractReviewSteps(AgentTask agentTask) {
        List<TaskStep> steps = new ArrayList<>();

        steps.add(createTaskStep(agentTask, 1, "关联方信息检索", "检索合同关联方信息"));
        steps.add(createTaskStep(agentTask, 2, "法规标准检索", "检索相关法规标准"));
        steps.add(createTaskStep(agentTask, 3, "内部制度检索", "检索企业内部制度"));
        steps.add(createTaskStep(agentTask, 4, "关联方审查", "审查合同关联方"));
        steps.add(createTaskStep(agentTask, 5, "条款分析", "分析合同条款"));
        steps.add(createTaskStep(agentTask, 6, "法律合规检查", "检查法律合规性"));
        steps.add(createTaskStep(agentTask, 7, "内部制度符合性检查", "检查内部制度符合性"));
        steps.add(createTaskStep(agentTask, 8, "风险评估", "评估合同风险"));
        steps.add(createTaskStep(agentTask, 9, "结果整合", "整合审查结果"));

        return steps;
    }

    /**
     * 创建单个任务步骤
     */
    private TaskStep createTaskStep(AgentTask agentTask, int stepOrder, String stepName, String description) {
        TaskStep step = new TaskStep();
        step.setTenantId(agentTask.getTenantId());
        step.setAgentTask(agentTask);
        step.setStepOrder(stepOrder);
        step.setStepName(stepName);
        step.setStepDescription(description);
        step.setStatus(TaskStepStatus.PENDING);
        step.setRetryCount(0);
        step.setVersion(1);
        step.setCreatedBy("system");
        step.setCreatedAt(Instant.now());
        step.setUpdatedBy("system");
        step.setUpdatedAt(Instant.now());
        step.setIsDeleted(false);

        return step;
    }

    /**
     * 更新步骤状态
     *
     * @param stepId 步骤ID
     * @param status 新状态
     */
    public void updateStepStatus(Long stepId, TaskStepStatus status) {
        log.debug("更新步骤状态，步骤ID: {}, 状态: {}", stepId, status);

        taskStepRepository
            .findById(stepId)
            .ifPresent(step -> {
                step.setStatus(status);
                step.setUpdatedAt(Instant.now());
                step.setUpdatedBy("system");

                if (status == TaskStepStatus.RUNNING) {
                    step.setStartTime(Instant.now());
                } else if (status == TaskStepStatus.COMPLETED || status == TaskStepStatus.FAILED) {
                    step.setEndTime(Instant.now());
                    if (step.getStartTime() != null) {
                        step.setExecutionTime(step.getEndTime().toEpochMilli() - step.getStartTime().toEpochMilli());
                    }
                }

                taskStepRepository.save(step);
            });
    }

    /**
     * 获取任务的所有步骤
     *
     * @param taskId 任务ID
     * @return 步骤列表
     */
    public List<TaskStep> getTaskSteps(Long taskId) {
        return taskStepRepository.findByAgentTaskIdOrderByStepOrder(taskId);
    }

    /**
     * 计算任务执行进度
     *
     * @param taskId 任务ID
     * @return 进度百分比（0-100）
     */
    public int calculateTaskProgress(Long taskId) {
        List<TaskStep> steps = getTaskSteps(taskId);
        if (steps.isEmpty()) {
            return 0;
        }

        long completedSteps = steps.stream().mapToLong(step -> step.getStatus() == TaskStepStatus.COMPLETED ? 1 : 0).sum();

        return (int) ((completedSteps * 100) / steps.size());
    }

    /**
     * 检查任务是否完成
     *
     * @param taskId 任务ID
     * @return 是否完成
     */
    public boolean isTaskCompleted(Long taskId) {
        List<TaskStep> steps = getTaskSteps(taskId);
        return steps.stream().allMatch(step -> step.getStatus() == TaskStepStatus.COMPLETED || step.getStatus() == TaskStepStatus.SKIPPED);
    }

    /**
     * 检查任务是否失败
     *
     * @param taskId 任务ID
     * @return 是否失败
     */
    public boolean isTaskFailed(Long taskId) {
        List<TaskStep> steps = getTaskSteps(taskId);
        return steps.stream().anyMatch(step -> step.getStatus() == TaskStepStatus.FAILED);
    }
}
