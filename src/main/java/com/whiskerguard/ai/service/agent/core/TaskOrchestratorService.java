/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：TaskOrchestratorService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：任务编排服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import com.whiskerguard.ai.service.agent.core.processor.StepProcessor;
import com.whiskerguard.ai.service.agent.core.processor.StepProcessorFactory;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 统一任务编排服务
 * <p>
 * 合并了原TaskOrchestratorService和EnhancedTaskOrchestratorServiceImpl的功能，
 * 提供完整的TaskStep生命周期管理：创建、执行、状态更新、进度跟踪。
 *
 * 主要功能：
 * 1. 任务步骤规划和创建
 * 2. 工作流执行（串行、并行、异步）
 * 3. 步骤状态管理和更新
 * 4. 执行进度跟踪和监控
 * 5. 错误处理和重试机制
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service("taskOrchestratorService")
@Transactional
public class TaskOrchestratorService implements TaskOrchestratorServiceInterface {

    private static final Logger log = LoggerFactory.getLogger(TaskOrchestratorService.class);

    private final TaskStepRepository taskStepRepository;
    private final TaskStepCoreService taskStepService;
    private final AgentContextCoreService contextService;
    private final StepProcessorFactory processorFactory;
    private final Executor asyncExecutor;

    // 存储正在执行的工作流
    private final Map<Long, CompletableFuture<Void>> runningWorkflows = new ConcurrentHashMap<>();

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public TaskOrchestratorService(
        TaskStepRepository taskStepRepository,
        TaskStepCoreService taskStepService,
        AgentContextCoreService contextService,
        StepProcessorFactory processorFactory,
        @Qualifier("taskExecutor") Executor asyncExecutor
    ) {
        this.taskStepRepository = taskStepRepository;
        this.taskStepService = taskStepService;
        this.contextService = contextService;
        this.processorFactory = processorFactory;
        this.asyncExecutor = asyncExecutor;
    }

    /**
     * 为Agent任务创建执行步骤
     *
     * @param agentTask 任务实体
     * @return 创建的步骤列表
     */
    public List<TaskStep> createTaskSteps(AgentTask agentTask) {
        log.info("为任务创建执行步骤，任务ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

        List<TaskStep> steps = new ArrayList<>();

        switch (agentTask.getTaskType()) {
            case "REGULATION_INTERNALIZATION" -> steps = createRegulationInternalizationSteps(agentTask);
            case "POLICY_REVIEW" -> steps = createPolicyReviewSteps(agentTask);
            case "CONTRACT_REVIEW" -> steps = createContractReviewSteps(agentTask);
            default -> throw new IllegalArgumentException("不支持的任务类型: " + agentTask.getTaskType());
        }

        // 保存步骤到数据库
        steps = taskStepRepository.saveAll(steps);

        log.info("任务步骤创建完成，任务ID: {}, 步骤数量: {}", agentTask.getId(), steps.size());
        return steps;
    }

    /**
     * 创建外规内化任务步骤
     */
    private List<TaskStep> createRegulationInternalizationSteps(AgentTask agentTask) {
        List<TaskStep> steps = new ArrayList<>();

        steps.add(createTaskStep(agentTask, 1, "法规内容检索", "从RAG服务检索相关法规内容"));
        steps.add(createTaskStep(agentTask, 2, "行业实践检索", "检索行业最佳实践案例"));
        steps.add(createTaskStep(agentTask, 3, "企业制度检索", "检索企业现有相关制度"));
        steps.add(createTaskStep(agentTask, 4, "内部制度生成", "基于法规和企业特点生成内部制度"));
        steps.add(createTaskStep(agentTask, 5, "合规性验证", "验证生成制度的合规性"));
        steps.add(createTaskStep(agentTask, 6, "结果整合", "整合所有结果并生成最终响应"));

        return steps;
    }

    /**
     * 创建制度审查任务步骤
     */
    private List<TaskStep> createPolicyReviewSteps(AgentTask agentTask) {
        List<TaskStep> steps = new ArrayList<>();

        steps.add(createTaskStep(agentTask, 1, "法规标准检索", "检索相关法规标准"));
        steps.add(createTaskStep(agentTask, 2, "行业实践检索", "检索行业最佳实践"));
        steps.add(createTaskStep(agentTask, 3, "历史制度检索", "检索企业历史制度"));
        steps.add(createTaskStep(agentTask, 4, "内容结构分析", "分析制度内容和结构"));
        steps.add(createTaskStep(agentTask, 5, "合规性检查", "检查制度合规性"));
        steps.add(createTaskStep(agentTask, 6, "风险点识别", "识别潜在风险点"));
        steps.add(createTaskStep(agentTask, 7, "优化建议生成", "生成优化建议"));
        steps.add(createTaskStep(agentTask, 8, "结果整合", "整合审查结果"));

        return steps;
    }

    /**
     * 创建合同审查任务步骤
     */
    private List<TaskStep> createContractReviewSteps(AgentTask agentTask) {
        List<TaskStep> steps = new ArrayList<>();

        steps.add(createTaskStep(agentTask, 1, "关联方信息检索", "检索合同关联方信息"));
        steps.add(createTaskStep(agentTask, 2, "法规标准检索", "检索相关法规标准"));
        steps.add(createTaskStep(agentTask, 3, "内部制度检索", "检索企业内部制度"));
        steps.add(createTaskStep(agentTask, 4, "关联方审查", "审查合同关联方"));
        steps.add(createTaskStep(agentTask, 5, "条款分析", "分析合同条款"));
        steps.add(createTaskStep(agentTask, 6, "法律合规检查", "检查法律合规性"));
        steps.add(createTaskStep(agentTask, 7, "内部制度符合性检查", "检查内部制度符合性"));
        steps.add(createTaskStep(agentTask, 8, "风险评估", "评估合同风险"));
        steps.add(createTaskStep(agentTask, 9, "结果整合", "整合审查结果"));

        return steps;
    }

    /**
     * 创建单个任务步骤
     */
    private TaskStep createTaskStep(AgentTask agentTask, int stepOrder, String stepName, String description) {
        TaskStep step = new TaskStep();
        step.setTenantId(agentTask.getTenantId());
        step.setAgentTask(agentTask);
        step.setStepOrder(stepOrder);
        step.setStepName(stepName);
        step.setStepDescription(description);
        step.setStatus(TaskStepStatus.PENDING);
        step.setRetryCount(0);
        step.setVersion(1);
        step.setCreatedBy("system");
        step.setCreatedAt(Instant.now());
        step.setUpdatedBy("system");
        step.setUpdatedAt(Instant.now());
        step.setIsDeleted(false);

        return step;
    }

    /**
     * 更新步骤状态
     *
     * @param stepId 步骤ID
     * @param status 新状态
     */
    public void updateStepStatus(Long stepId, TaskStepStatus status) {
        log.debug("更新步骤状态，步骤ID: {}, 状态: {}", stepId, status);

        taskStepRepository
            .findById(stepId)
            .ifPresent(step -> {
                step.setStatus(status);
                step.setUpdatedAt(Instant.now());
                step.setUpdatedBy("system");

                if (status == TaskStepStatus.RUNNING) {
                    step.setStartTime(Instant.now());
                } else if (status == TaskStepStatus.COMPLETED || status == TaskStepStatus.FAILED) {
                    step.setEndTime(Instant.now());
                    if (step.getStartTime() != null) {
                        step.setExecutionTime(step.getEndTime().toEpochMilli() - step.getStartTime().toEpochMilli());
                    }
                }

                taskStepRepository.save(step);
            });
    }

    /**
     * 获取任务的所有步骤
     *
     * @param taskId 任务ID
     * @return 步骤列表
     */
    public List<TaskStep> getTaskSteps(Long taskId) {
        return taskStepRepository.findByAgentTaskIdOrderByStepOrder(taskId);
    }

    /**
     * 计算任务执行进度
     *
     * @param taskId 任务ID
     * @return 进度百分比（0-100）
     */
    public int calculateTaskProgress(Long taskId) {
        List<TaskStep> steps = getTaskSteps(taskId);
        if (steps.isEmpty()) {
            return 0;
        }

        long completedSteps = steps.stream().mapToLong(step -> step.getStatus() == TaskStepStatus.COMPLETED ? 1 : 0).sum();

        return (int) ((completedSteps * 100) / steps.size());
    }

    /**
     * 检查任务是否完成
     *
     * @param taskId 任务ID
     * @return 是否完成
     */
    public boolean isTaskCompleted(Long taskId) {
        List<TaskStep> steps = getTaskSteps(taskId);
        return steps.stream().allMatch(step -> step.getStatus() == TaskStepStatus.COMPLETED || step.getStatus() == TaskStepStatus.SKIPPED);
    }

    /**
     * 检查任务是否失败
     *
     * @param taskId 任务ID
     * @return 是否失败
     */
    public boolean isTaskFailed(Long taskId) {
        List<TaskStep> steps = getTaskSteps(taskId);
        return steps.stream().anyMatch(step -> step.getStatus() == TaskStepStatus.FAILED);
    }

    // ========== 工作流执行方法（来自EnhancedTaskOrchestratorServiceImpl）==========

    @Override
    public void executeWorkflow(Long agentTaskId, List<StepDefinition> steps) {
        log.info("开始执行工作流，任务ID: {}, 步骤数: {}", agentTaskId, steps.size());

        try {
            // 获取上下文
            AgentContext context = contextService.getContextByTaskId(agentTaskId);

            // 按顺序执行步骤
            for (StepDefinition stepDef : steps) {
                executeStepInternal(agentTaskId, stepDef, context);
            }

            log.info("工作流执行完成，任务ID: {}", agentTaskId);
        } catch (Exception e) {
            log.error("工作流执行失败，任务ID: {}", agentTaskId, e);
            throw new RuntimeException("工作流执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CompletableFuture<Void> executeWorkflowAsync(Long agentTaskId, List<StepDefinition> steps) {
        log.info("开始异步执行工作流，任务ID: {}, 步骤数: {}", agentTaskId, steps.size());

        CompletableFuture<Void> future = CompletableFuture.runAsync(
            () -> {
                executeWorkflow(agentTaskId, steps);
            },
            asyncExecutor
        );

        // 存储正在执行的工作流
        runningWorkflows.put(agentTaskId, future);

        // 完成后清理
        future.whenComplete((result, throwable) -> {
            runningWorkflows.remove(agentTaskId);
            if (throwable != null) {
                log.error("异步工作流执行失败，任务ID: {}", agentTaskId, throwable);
            } else {
                log.info("异步工作流执行完成，任务ID: {}", agentTaskId);
            }
        });

        return future;
    }

    @Override
    public void executeWorkflowParallel(Long agentTaskId, List<StepDefinition> parallelSteps) {
        log.info("开始并行执行工作流步骤，任务ID: {}, 并行步骤数: {}", agentTaskId, parallelSteps.size());

        try {
            // 获取上下文
            AgentContext context = contextService.getContextByTaskId(agentTaskId);

            // 创建并行任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (StepDefinition stepDef : parallelSteps) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> {
                        executeStepInternal(agentTaskId, stepDef, context);
                    },
                    asyncExecutor
                );
                futures.add(future);
            }

            // 等待所有并行步骤完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("并行工作流步骤执行完成，任务ID: {}", agentTaskId);
        } catch (Exception e) {
            log.error("并行工作流步骤执行失败，任务ID: {}", agentTaskId, e);
            throw new RuntimeException("并行工作流步骤执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String executeStep(Long agentTaskId, StepDefinition stepDefinition) {
        log.debug("执行单个步骤，任务ID: {}, 步骤: {}", agentTaskId, stepDefinition.getName());

        try {
            // 获取上下文
            AgentContext context = contextService.getContextByTaskId(agentTaskId);

            return executeStepInternal(agentTaskId, stepDefinition, context);
        } catch (Exception e) {
            log.error("步骤执行失败，任务ID: {}, 步骤: {}", agentTaskId, stepDefinition.getName(), e);
            throw new RuntimeException("步骤执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean retryStep(Long stepId) {
        log.info("重试步骤，步骤ID: {}", stepId);

        try {
            // 获取步骤信息
            TaskStep step = taskStepService.getStep(stepId);
            if (step == null) {
                log.warn("步骤不存在，无法重试，步骤ID: {}", stepId);
                return false;
            }

            // 检查重试次数
            if (step.getRetryCount() >= 3) {
                log.warn("步骤重试次数已达上限，步骤ID: {}", stepId);
                return false;
            }

            // 重置步骤状态
            taskStepService.updateStepStatus(stepId, TaskStepStatus.PENDING);
            taskStepService.incrementRetryCount(stepId);

            // 这里可以添加重新执行步骤的逻辑
            log.info("步骤重试成功，步骤ID: {}", stepId);
            return true;
        } catch (Exception e) {
            log.error("步骤重试失败，步骤ID: {}", stepId, e);
            return false;
        }
    }

    @Override
    public boolean cancelWorkflow(Long agentTaskId) {
        log.info("取消工作流，任务ID: {}", agentTaskId);

        try {
            // 取消正在运行的异步工作流
            CompletableFuture<Void> runningWorkflow = runningWorkflows.get(agentTaskId);
            if (runningWorkflow != null) {
                runningWorkflow.cancel(true);
                runningWorkflows.remove(agentTaskId);
            }

            // 更新所有运行中的步骤状态为取消
            List<TaskStep> runningSteps = taskStepService.getStepsByStatusAndTask(agentTaskId, TaskStepStatus.RUNNING);

            for (TaskStep step : runningSteps) {
                taskStepService.updateStepStatus(step.getId(), TaskStepStatus.CANCELLED);
            }

            log.info("工作流取消成功，任务ID: {}", agentTaskId);
            return true;
        } catch (Exception e) {
            log.error("工作流取消失败，任务ID: {}", agentTaskId, e);
            return false;
        }
    }

    @Override
    public TaskOrchestratorServiceInterface.WorkflowStatus getWorkflowStatus(Long agentTaskId) {
        try {
            List<TaskStep> steps = getTaskSteps(agentTaskId);

            int totalSteps = steps.size();
            int completedSteps = (int) steps.stream().filter(step -> step.getStatus() == TaskStepStatus.COMPLETED).count();
            int failedSteps = (int) steps.stream().filter(step -> step.getStatus() == TaskStepStatus.FAILED).count();

            boolean isRunning = steps.stream().anyMatch(step -> step.getStatus() == TaskStepStatus.RUNNING);
            boolean isCompleted = completedSteps == totalSteps;
            boolean isFailed = failedSteps > 0;

            String lastError = steps.stream()
                .filter(step -> step.getErrorMessage() != null)
                .map(TaskStep::getErrorMessage)
                .reduce((first, second) -> second)
                .orElse(null);

            return new TaskOrchestratorServiceInterface.WorkflowStatus(
                isRunning, isCompleted, isFailed, totalSteps, completedSteps, failedSteps, lastError
            );
        } catch (Exception e) {
            log.error("获取工作流状态失败，任务ID: {}", agentTaskId, e);
            return new TaskOrchestratorServiceInterface.WorkflowStatus(
                false, false, true, 0, 0, 1, e.getMessage()
            );
        }
    }

    @Override
    public boolean resumeWorkflow(Long agentTaskId) {
        log.info("恢复工作流，任务ID: {}", agentTaskId);

        try {
            // 获取所有失败的步骤
            List<TaskStep> failedSteps = taskStepService.getStepsByStatusAndTask(agentTaskId, TaskStepStatus.FAILED);

            if (failedSteps.isEmpty()) {
                log.info("没有失败的步骤需要恢复，任务ID: {}", agentTaskId);
                return true;
            }

            // 重试所有失败的步骤
            boolean allRetrySuccessful = true;
            for (TaskStep step : failedSteps) {
                if (!retryStep(step.getId())) {
                    allRetrySuccessful = false;
                }
            }

            log.info("工作流恢复完成，任务ID: {}, 成功: {}", agentTaskId, allRetrySuccessful);
            return allRetrySuccessful;
        } catch (Exception e) {
            log.error("工作流恢复失败，任务ID: {}", agentTaskId, e);
            return false;
        }
    }

    // ========== 内部执行方法 ==========

    /**
     * 内部步骤执行方法
     * 统一的步骤执行逻辑，确保数据库中的TaskStep状态正确更新
     */
    private String executeStepInternal(Long agentTaskId, StepDefinition stepDef, AgentContext context) {
        log.debug("执行步骤内部逻辑，任务ID: {}, 步骤: {}", agentTaskId, stepDef.getName());

        TaskStep step = null;

        try {
            // 查找数据库中已存在的TaskStep记录（由createTaskSteps创建）
            step = findExistingTaskStep(agentTaskId, stepDef);

            if (step == null) {
                // 如果没有找到，创建新的步骤记录
                step = taskStepService.createStep(agentTaskId, stepDef.getName(), stepDef.getType(), stepDef.getOrder());
            } else {
                // 更新步骤类型（如果数据库中为空）
                if (step.getStepType() == null && stepDef.getType() != null) {
                    step.setStepType(stepDef.getType());
                    taskStepService.updateStep(step);
                }
            }

            // 开始步骤执行
            taskStepService.startStep(step.getId());

            // 准备输入数据
            String input = prepareStepInput(context, stepDef);
            taskStepService.recordInput(step.getId(), input);

            // 准备步骤参数
            Map<String, Object> parameters = prepareStepParameters(stepDef, context);

            // 获取处理器并执行
            StepProcessor processor = processorFactory.getProcessor(stepDef.getType());
            String output = processor.process(input, parameters);

            // 完成步骤
            taskStepService.completeStep(step.getId(), output);

            // 存储结果到上下文
            if (stepDef.getOutputKey() != null) {
                contextService.storeVariable(context.getId(), stepDef.getOutputKey(), output);
            }

            log.debug("步骤执行成功，步骤ID: {}, 输出长度: {}", step.getId(), output != null ? output.length() : 0);

            return output;
        } catch (Exception e) {
            log.error("步骤执行失败，步骤定义: {}", stepDef.getName(), e);

            // 记录错误
            if (step != null) {
                taskStepService.failStep(step.getId(), e.getMessage());
            }

            // 根据策略决定是否重新抛出异常
            if (stepDef.isRequired()) {
                throw new RuntimeException("必要步骤执行失败: " + stepDef.getName(), e);
            }

            return null; // 非必要步骤失败时返回null
        }
    }

    /**
     * 查找数据库中已存在的TaskStep记录
     */
    private TaskStep findExistingTaskStep(Long agentTaskId, StepDefinition stepDef) {
        try {
            // 根据任务ID获取所有步骤
            List<TaskStep> existingSteps = taskStepService.getStepsByTaskId(agentTaskId);

            // 根据步骤名称和顺序查找匹配的步骤
            return existingSteps.stream()
                .filter(step -> stepDef.getName().equals(step.getStepName()) ||
                               (stepDef.getOrder() != null && stepDef.getOrder().equals(step.getStepOrder())))
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("查找已存在的TaskStep失败，任务ID: {}, 步骤: {}", agentTaskId, stepDef.getName(), e);
            return null;
        }
    }

    /**
     * 准备步骤输入数据
     */
    private String prepareStepInput(AgentContext context, StepDefinition stepDef) {
        // 简化实现，实际可以根据stepDef的要求从context中提取数据
        return stepDef.getInputTemplate() != null ? stepDef.getInputTemplate() : "";
    }

    /**
     * 准备步骤参数
     */
    private Map<String, Object> prepareStepParameters(StepDefinition stepDef, AgentContext context) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("stepName", stepDef.getName());
        parameters.put("stepType", stepDef.getType());
        parameters.put("contextId", context.getId());
        parameters.put("agentTaskId", context.getAgentTaskId());
        return parameters;
    }
}
