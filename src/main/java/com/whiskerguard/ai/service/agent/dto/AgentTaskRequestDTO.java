/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：Agent任务请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * Agent任务请求DTO
 * <p>
 * 用于接收客户端的Agent任务创建请求。
 * 包含任务的基本信息和执行参数。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "Agent任务请求")
public class AgentTaskRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    /**
     * 任务类型
     */
    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型", required = true, example = "REGULATION_INTERNALIZATION")
    private AgentTaskType taskType;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(max = 200, message = "任务标题长度不能超过200个字符")
    @Schema(description = "任务标题", required = true, example = "电力行业安全生产法规内化")
    private String title;

    /**
     * 任务描述
     */
    @Size(max = 1000, message = "任务描述长度不能超过1000个字符")
    @Schema(description = "任务描述", example = "将国家电力安全生产相关法规转化为公司内部管理制度")
    private String description;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级", example = "NORMAL", allowableValues = {"HIGH", "LOW", "NORMAL", "URGENT"})
    private String priority;

    /**
     * 请求数据（JSON格式）
     */
    @NotBlank(message = "请求数据不能为空")
    @Schema(description = "请求数据（JSON格式）", required = true)
    private String requestData;

    /**
     * 扩展元数据（JSON格式）
     */
    @Schema(description = "扩展元数据（JSON格式）")
    private String metadata;

    // 默认构造函数
    public AgentTaskRequestDTO() {}

    // 全参构造函数
    public AgentTaskRequestDTO(
        Long tenantId,
        AgentTaskType taskType,
        String title,
        String description,
        String priority,
        String requestData,
        String metadata
    ) {
        this.tenantId = tenantId;
        this.taskType = taskType;
        this.title = title;
        this.description = description;
        this.priority = priority;
        this.requestData = requestData;
        this.metadata = metadata;
    }

    // Getter和Setter方法
    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public AgentTaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(AgentTaskType taskType) {
        this.taskType = taskType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getRequestData() {
        return requestData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    // Builder模式的静态方法
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private Long tenantId;
        private AgentTaskType taskType;
        private String title;
        private String description;
        private String priority;
        private String requestData;
        private String metadata;

        public Builder tenantId(Long tenantId) {
            this.tenantId = tenantId;
            return this;
        }

        public Builder taskType(AgentTaskType taskType) {
            this.taskType = taskType;
            return this;
        }

        public Builder title(String title) {
            this.title = title;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder priority(String priority) {
            this.priority = priority;
            return this;
        }

        public Builder requestData(String requestData) {
            this.requestData = requestData;
            return this;
        }

        public Builder metadata(String metadata) {
            this.metadata = metadata;
            return this;
        }

        public AgentTaskRequestDTO build() {
            return new AgentTaskRequestDTO(tenantId, taskType, title, description, priority, requestData, metadata);
        }
    }
}
