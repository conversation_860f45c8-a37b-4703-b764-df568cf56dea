package com.whiskerguard.ai.service.agent.core.processor.impl;

import com.whiskerguard.ai.service.agent.core.KnowledgeRetrievalService;
import com.whiskerguard.ai.service.agent.core.processor.StepProcessor;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 检索步骤处理器
 * Retrieval Step Processor
 *
 * 处理知识检索相关的步骤
 * Process knowledge retrieval related steps
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class RetrievalStepProcessor implements StepProcessor {

    private static final Logger log = LoggerFactory.getLogger(RetrievalStepProcessor.class);

    private final KnowledgeRetrievalService retrievalService;

    public RetrievalStepProcessor(KnowledgeRetrievalService retrievalService) {
        this.retrievalService = retrievalService;
    }

    @Override
    public String getType() {
        return "RETRIEVAL";
    }

    @Override
    public String process(String input, Map<String, Object> parameters) {
        log.debug("开始处理检索步骤，输入: {}, 参数: {}", input, parameters);

        try {
            // 解析参数
            String retrievalType = (String) parameters.get("type");
            Long tenantId = (Long) parameters.get("tenantId");
            String cacheKey = (String) parameters.getOrDefault("cacheKey", UUID.randomUUID().toString());

            // 验证必要参数
            if (retrievalType == null) {
                throw new IllegalArgumentException("检索类型不能为空");
            }

            // 从输入中解析查询参数
            Map<String, Object> queryParams = parseQueryParams(input, parameters);

            // 调用知识检索服务
            String result = retrievalService.retrieveKnowledge(tenantId, retrievalType, cacheKey, queryParams);

            log.debug("检索步骤处理完成，结果长度: {}", result != null ? result.length() : 0);
            return result;
        } catch (Exception e) {
            log.error("检索步骤处理失败", e);
            throw new RuntimeException("检索步骤处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateParameters(Map<String, Object> parameters) {
        // 检查必要参数
        if (!parameters.containsKey("type")) {
            return false;
        }

        String type = (String) parameters.get("type");
        return type != null && !type.trim().isEmpty();
    }

    @Override
    public String getDescription() {
        return "知识检索步骤处理器，支持多种类型的知识检索操作";
    }

    /**
     * 解析查询参数
     * Parse query parameters
     *
     * @param input 输入数据 / Input data
     * @param parameters 步骤参数 / Step parameters
     * @return 查询参数映射 / Query parameter map
     */
    private Map<String, Object> parseQueryParams(String input, Map<String, Object> parameters) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("query", input);
        queryParams.put("maxResults", parameters.getOrDefault("maxResults", 10));
        queryParams.put("minScore", parameters.getOrDefault("minScore", 0.0));

        // 添加其他可选参数
        if (parameters.containsKey("categories")) {
            queryParams.put("categories", parameters.get("categories"));
        }

        if (parameters.containsKey("dateRange")) {
            queryParams.put("dateRange", parameters.get("dateRange"));
        }

        return queryParams;
    }
}
