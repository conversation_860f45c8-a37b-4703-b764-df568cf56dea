package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;

/**
 * Agent上下文核心服务接口
 * Agent Context Core Service Interface
 *
 * 提供Agent任务执行过程中的上下文管理功能
 * Provide context management functions during Agent task execution
 *
 * 主要功能：
 * - 创建和管理任务上下文
 * - 存储和检索上下文变量
 * - 管理临时文件引用
 * - 记录执行历史
 *
 * Main functions:
 * - Create and manage task contexts
 * - Store and retrieve context variables
 * - Manage temporary file references
 * - Record execution history
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface AgentContextCoreService {
    /**
     * 创建新的任务上下文
     * Create a new task context
     *
     * @param agentTaskId 关联的任务ID / Associated task ID
     * @param contextType 上下文类型 / Context type
     * @return 创建的上下文实体 / Created context entity
     */
    AgentContext createContext(Long agentTaskId, String contextType);

    /**
     * 根据任务ID获取任务上下文
     * Get task context by task ID
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 上下文实体 / Context entity
     * @throws RuntimeException 如果上下文不存在 / If context doesn't exist
     */
    AgentContext getContextByTaskId(Long agentTaskId);

    /**
     * 根据任务ID获取或创建任务上下文
     * Get or create task context by task ID
     *
     * @param agentTaskId 任务ID / Task ID
     * @return 上下文实体 / Context entity
     */
    AgentContext getOrCreateContextByTaskId(Long agentTaskId);

    /**
     * 存储上下文变量
     * Store context variable
     *
     * @param contextId 上下文ID / Context ID
     * @param key 变量键 / Variable key
     * @param value 变量值 / Variable value
     */
    void storeVariable(Long contextId, String key, Object value);

    /**
     * 获取上下文变量
     * Get context variable
     *
     * @param contextId 上下文ID / Context ID
     * @param key 变量键 / Variable key
     * @param valueType 值类型 / Value type
     * @param <T> 返回值类型 / Return value type
     * @return 变量值 / Variable value
     */
    <T> T getVariable(Long contextId, String key, Class<T> valueType);

    /**
     * 检查上下文变量是否存在
     * Check if context variable exists
     *
     * @param contextId 上下文ID / Context ID
     * @param key 变量键 / Variable key
     * @return 是否存在 / Whether exists
     */
    boolean hasVariable(Long contextId, String key);

    /**
     * 移除上下文变量
     * Remove context variable
     *
     * @param contextId 上下文ID / Context ID
     * @param key 变量键 / Variable key
     */
    void removeVariable(Long contextId, String key);

    /**
     * 添加临时文件引用
     * Add temporary file reference
     *
     * @param contextId 上下文ID / Context ID
     * @param fileKey 文件键 / File key
     * @param filePath 文件路径 / File path
     */
    void addTempFile(Long contextId, String fileKey, String filePath);

    /**
     * 获取临时文件路径
     * Get temporary file path
     *
     * @param contextId 上下文ID / Context ID
     * @param fileKey 文件键 / File key
     * @return 文件路径 / File path
     */
    String getTempFilePath(Long contextId, String fileKey);

    /**
     * 移除临时文件引用
     * Remove temporary file reference
     *
     * @param contextId 上下文ID / Context ID
     * @param fileKey 文件键 / File key
     */
    void removeTempFile(Long contextId, String fileKey);

    /**
     * 记录上下文历史
     * Record context history
     *
     * @param contextId 上下文ID / Context ID
     * @param action 动作 / Action
     * @param details 详细信息 / Details
     */
    void recordHistory(Long contextId, String action, String details);

    /**
     * 更新上下文数据
     * Update context data
     *
     * @param contextId 上下文ID / Context ID
     * @param contextData 上下文数据 / Context data
     */
    void updateContextData(Long contextId, String contextData);

    /**
     * 获取上下文数据
     * Get context data
     *
     * @param contextId 上下文ID / Context ID
     * @return 上下文数据 / Context data
     */
    String getContextData(Long contextId);

    /**
     * 清理过期的上下文
     * Clean up expired contexts
     *
     * @param taskType 任务类型（可选，为null时清理所有类型）/ Task type (optional, clean all types if null)
     * @return 清理的上下文数量 / Number of cleaned contexts
     */
    int cleanupExpiredContexts(AgentTaskType taskType);
}
