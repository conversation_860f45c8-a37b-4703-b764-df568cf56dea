/**
 * ChatLaw 调用实现类
 * <p>
 * 负责与 ChatLaw API 进行通信，专门用于法律相关任务。
 */
package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.client.ChatLawApiClient;
import com.whiskerguard.ai.client.dto.ChatLawResponseDTO;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.util.RateLimitHandler;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
public class ChatLawInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(ChatLawInvoker.class);
    private final ChatLawApiClient chatLawApiClient;
    private final RagHelper ragHelper;
    private final RateLimitHandler rateLimitHandler;

    public ChatLawInvoker(ChatLawApiClient chatLawApiClient, RagHelper ragHelper, RateLimitHandler rateLimitHandler) {
        this.chatLawApiClient = chatLawApiClient;
        this.ragHelper = ragHelper;
        this.rateLimitHandler = rateLimitHandler;
    }

    @Override
    public boolean supportsStreaming() {
        return false;
    }

    @Override
    public String getToolKey() {
        return "chatlaw";
    }

    @Override
    @Bulkhead(name = "chatlaw", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "chatlaw")
    @Retry(name = "chatlaw")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        String apiKey = "chatlaw";

        try {
            // 检查是否需要延迟调用
            long delayMs = rateLimitHandler.shouldDelayCall(apiKey);
            if (delayMs > 0) {
                log.info("ChatLaw API调用频率控制，延迟{}ms", delayMs);
                Thread.sleep(delayMs);
            }
            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("原始提示词: {}", originalPrompt);
            log.debug("增强提示词: {}", enhancedPrompt);

            log.debug("ChatLaw请求体: {}", enhancedPrompt);

            long start = System.currentTimeMillis();
            ChatLawResponseDTO resp = chatLawApiClient.queryLegalModel(enhancedPrompt);
            long duration = System.currentTimeMillis() - start;

            log.debug("ChatLaw响应: {}", resp);

            // ChatLaw 响应解析
            String content = resp.getChoices().get(0).getMessage().getContent();

            // 提取使用量信息
            Map<String, Object> usage = new HashMap<>();
            if (resp.getUsage() != null) {
                usage.put("prompt_tokens", resp.getUsage().getPromptTokens());
                usage.put("completion_tokens", resp.getUsage().getCompletionTokens());
                usage.put("total_tokens", resp.getUsage().getTotalTokens());
            }

            // 记录成功调用
            rateLimitHandler.recordSuccessfulCall(apiKey);

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            // 检查是否为速率限制错误
            if (rateLimitHandler.isRateLimitError(e)) {
                rateLimitHandler.recordRateLimitError(apiKey);
                log.warn("ChatLaw API遇到速率限制: {}", e.getMessage());

                // 使用RateLimitHandler提供的专业建议
                String advice = rateLimitHandler.getErrorAdvice(apiKey, e);
                String errorMessage = "调用ChatLaw API失败: " + e.getMessage() + "\n" + advice;

                // 对于429错误，重新抛出以触发Resilience4j重试机制
                throw new RuntimeException("429 Too Many Requests - Rate limit exceeded");
            }

            log.error("调用ChatLaw API失败", e);

            // 根据错误类型提供更详细的错误信息
            String errorMessage = "调用ChatLaw API失败: " + e.getMessage();
            if (e.getMessage() != null) {
                if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                    errorMessage += "\n可能的原因：\n1. API Key 无效或已过期\n2. 未开通对应的模型服务\n3. API Key 格式不正确";
                } else if (e.getMessage().contains("403") || e.getMessage().contains("Forbidden")) {
                    errorMessage += "\n可能的原因：\n1. API Key 权限不足\n2. 账号余额不足\n3. 请求频率超限";
                } else if (e.getMessage().contains("timeout") || e.getMessage().contains("connect")) {
                    errorMessage += "\n可能的原因：\n1. 网络连接问题\n2. ChatLaw服务暂时不可用\n3. 请求超时";
                }
            }

            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            return AiResult.builder().content(errorMessage).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        // ChatLaw 当前不支持流式输出，使用默认实现
        return AiToolInvoker.super.invokeStream(dto, cfg);
    }
}
