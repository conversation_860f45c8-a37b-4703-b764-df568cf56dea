/**
 * ChatGPT 调用实现类
 * <p>
 * 负责与 OpenAI 的 ChatGPT API 进行通信，支持 RAG 增强功能。
 * 支持流式输出功能。
 */
package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.util.RateLimitHandler;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

@Component
public class ChatGptInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(ChatGptInvoker.class);
    private final WebClient.Builder webClientBuilder;
    private final RagHelper ragHelper;
    private final RateLimitHandler rateLimitHandler;
    private final ObjectMapper objectMapper;

    public ChatGptInvoker(
        WebClient.Builder webClientBuilder,
        RagHelper ragHelper,
        RateLimitHandler rateLimitHandler,
        ObjectMapper objectMapper
    ) {
        this.webClientBuilder = webClientBuilder;
        this.ragHelper = ragHelper;
        this.rateLimitHandler = rateLimitHandler;
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String getToolKey() {
        return "chatgpt";
    }

    @Override
    @Bulkhead(name = "chatgpt", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "chatgpt")
    @Retry(name = "chatgpt")
    @SuppressWarnings("unchecked")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        String apiKey = "chatgpt";

        try {
            // 检查是否需要延迟调用
            long delayMs = rateLimitHandler.shouldDelayCall(apiKey);
            if (delayMs > 0) {
                log.info("ChatGPT API调用频率控制，延迟{}ms", delayMs);
                Thread.sleep(delayMs);
            }
            WebClient client = webClientBuilder.baseUrl(cfg.getApiUrl()).defaultHeader(cfg.getAuthType(), cfg.getApiKey()).build();

            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("原始提示词: {}", originalPrompt);
            log.debug("增强提示词: {}", enhancedPrompt);

            Map<String, Object> body = new HashMap<>();
            body.put("prompt", enhancedPrompt); // 使用增强后的提示词
            if (dto.getMetadata() != null) {
                body.putAll(dto.getMetadata());
            }

            long start = System.currentTimeMillis();
            Map<String, Object> resp = client.post().uri(cfg.getPath()).bodyValue(body).retrieve().bodyToMono(Map.class).block();
            long duration = System.currentTimeMillis() - start;

            // 解析 OpenAI 风格响应
            List<Map<String, Object>> choices = (List<Map<String, Object>>) resp.get("choices");
            Map<String, Object> first = (Map<String, Object>) choices.get(0).get("message");
            String content = (String) first.get("content");

            Map<String, Object> usage = (Map<String, Object>) resp.get("usage");

            // 记录成功调用
            rateLimitHandler.recordSuccessfulCall(apiKey);

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            // 检查是否为速率限制错误
            if (rateLimitHandler.isRateLimitError(e)) {
                rateLimitHandler.recordRateLimitError(apiKey);
                log.warn("ChatGPT API遇到速率限制: {}", e.getMessage());

                // 使用RateLimitHandler提供的专业建议
                String advice = rateLimitHandler.getErrorAdvice(apiKey, e);
                String errorMessage = "调用ChatGPT API失败: " + e.getMessage() + "\n" + advice;

                // 对于429错误，重新抛出以触发Resilience4j重试机制
                throw new RuntimeException("429 Too Many Requests - Rate limit exceeded");
            }

            log.error("调用ChatGPT API失败", e);

            // 根据错误类型提供更详细的错误信息
            String errorMessage = "调用ChatGPT API失败: " + e.getMessage();
            if (e.getMessage() != null) {
                if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                    errorMessage += "\n可能的原因：\n1. API Key 无效或已过期\n2. 未开通对应的模型服务\n3. API Key 格式不正确";
                } else if (e.getMessage().contains("403") || e.getMessage().contains("Forbidden")) {
                    errorMessage += "\n可能的原因：\n1. API Key 权限不足\n2. 账号余额不足\n3. 请求频率超限";
                } else if (e.getMessage().contains("timeout") || e.getMessage().contains("connect")) {
                    errorMessage += "\n可能的原因：\n1. 网络连接问题\n2. OpenAI服务暂时不可用\n3. 请求超时";
                }
            }

            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            return AiResult.builder().content(errorMessage).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    @Bulkhead(name = "chatgpt-stream", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "chatgpt-stream")
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        WebClient client = webClientBuilder.baseUrl(cfg.getApiUrl()).defaultHeader(cfg.getAuthType(), cfg.getApiKey()).build();

        // 使用 RAG 增强提示词
        String originalPrompt = dto.getPrompt();
        String enhancedPrompt = ragHelper.enhancePromptWithRag(
            new AiInvocationRequestDTO(dto.getToolKey(), dto.getPrompt(), dto.getMetadata(), dto.getTenantId(), dto.getEmployeeId())
        );
        log.debug("流式调用 - 原始提示词: {}", originalPrompt);
        log.debug("流式调用 - 增强提示词: {}", enhancedPrompt);

        // 构建请求体
        Map<String, Object> body = new HashMap<>();
        body.put("prompt", enhancedPrompt);
        body.put("stream", true); // 启用流式输出
        if (dto.getMetadata() != null) {
            body.putAll(dto.getMetadata());
        }

        // 用于累积完整响应以计算最终使用量
        AtomicReference<StringBuilder> fullResponseBuilder = new AtomicReference<>(new StringBuilder());

        // 发起流式请求
        return client
            .post()
            .uri(cfg.getPath())
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body)
            .accept(MediaType.TEXT_EVENT_STREAM)
            .retrieve()
            .bodyToFlux(String.class)
            .map(chunk -> {
                try {
                    // 处理 SSE 格式的响应
                    if (chunk.startsWith("data: ")) {
                        chunk = chunk.substring(6);
                    }

                    // 处理流结束标记
                    if ("[DONE]".equals(chunk.trim())) {
                        // 创建最终使用量统计
                        Map<String, Object> usage = new HashMap<>();
                        usage.put("total_tokens", fullResponseBuilder.get().length() / 4); // 粗略估计
                        return new AiStreamResponseDTO("", usage);
                    }

                    // 解析 JSON 响应
                    Map<String, Object> response = objectMapper.readValue(chunk, Map.class);

                    // 提取内容
                    List<Map<String, Object>> choices = (List<Map<String, Object>>) response.get("choices");
                    Map<String, Object> choice = choices.get(0);

                    // 检查是否有 delta 字段（流式响应格式）
                    Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                    if (delta != null && delta.containsKey("content")) {
                        String content = (String) delta.get("content");
                        fullResponseBuilder.get().append(content);
                    }

                    // 如果没有 delta 或 content，返回空内容
                    return new AiStreamResponseDTO("", false);
                } catch (JsonProcessingException e) {
                    log.error("解析流式响应失败", e);
                    return new AiStreamResponseDTO("解析流式响应失败: " + e.getMessage());
                }
            })
            .onErrorResume(e -> {
                log.error("流式调用失败", e);
                return Flux.just(new AiStreamResponseDTO("流式调用失败: " + e.getMessage()));
            });
    }
}
