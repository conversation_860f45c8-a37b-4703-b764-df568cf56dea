/**
 * <PERSON> AI 调用实现类
 * <p>
 * 负责与 Claude API 进行通信，支持 RAG 增强功能。
 */
package com.whiskerguard.ai.service.invocation;

import com.anthropic.client.AnthropicClient;
import com.anthropic.client.okhttp.AnthropicOkHttpClient;
import com.anthropic.models.messages.ContentBlock;
import com.anthropic.models.messages.Message;
import com.anthropic.models.messages.MessageCreateParams;
import com.anthropic.models.messages.TextBlock;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.util.RateLimitHandler;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

/**
 * Claude AI 调用实现类，使用 WebClient 直接调用 Claude API
 */
@Component
public class ClaudeInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(ClaudeInvoker.class);
    private final WebClient.Builder webClientBuilder;
    private final RagHelper ragHelper;
    private final RateLimitHandler rateLimitHandler;
    private final ObjectMapper objectMapper;

    // 从配置文件读取Claude配置参数
    @Value("${whiskerguard.ai.claude.model:claude-3-opus-20240229}")
    private String defaultModel;

    @Value("${whiskerguard.ai.claude.max-tokens:4000}")
    private Integer defaultMaxTokens;

    @Value("${whiskerguard.ai.claude.temperature:0.7}")
    private Double defaultTemperature;

    public ClaudeInvoker(
        WebClient.Builder webClientBuilder,
        RagHelper ragHelper,
        RateLimitHandler rateLimitHandler,
        ObjectMapper objectMapper
    ) {
        this.webClientBuilder = webClientBuilder;
        this.ragHelper = ragHelper;
        this.rateLimitHandler = rateLimitHandler;
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String getToolKey() {
        return "claude";
    }

    /**
     * 非流式调用 Claude API
     */
    @Override
    @Bulkhead(name = "claude", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "claude")
    @Retry(name = "claude")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        String apiKey = "claude";
        long startTime = System.currentTimeMillis();

        try {
            // 检查是否需要延迟调用
            long delayMs = rateLimitHandler.shouldDelayCall(apiKey);
            if (delayMs > 0) {
                log.info("Claude API调用频率控制，延迟{}ms", delayMs);
                Thread.sleep(delayMs);
            }
            // 创建 Anthropic 客户端
            AnthropicClient anthropic = AnthropicOkHttpClient.builder().apiKey(cfg.getApiKey()).build();

            // 使用 RAG 增强提示词
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);

            // 构建消息参数
            MessageCreateParams params = MessageCreateParams.builder()
                .model(defaultModel)
                .maxTokens(defaultMaxTokens.longValue())
                .temperature(defaultTemperature)
                .addUserMessage(enhancedPrompt)
                .build();

            // 调用 Claude API
            Message response = anthropic.messages().create(params);

            // 提取响应内容
            String content = extractContentFromMessage(response);

            // 构建使用量信息
            Map<String, Object> usage = new HashMap<>();
            if (response.usage() != null) {
                usage.put("input_tokens", response.usage().inputTokens());
                usage.put("output_tokens", response.usage().outputTokens());
                usage.put("total_tokens", response.usage().inputTokens() + response.usage().outputTokens());
            }

            long duration = System.currentTimeMillis() - startTime;

            // 记录成功调用
            rateLimitHandler.recordSuccessfulCall(apiKey);

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            // 检查是否为速率限制错误
            if (rateLimitHandler.isRateLimitError(e)) {
                rateLimitHandler.recordRateLimitError(apiKey);
                log.warn("Claude API遇到速率限制: {}", e.getMessage());

                // 使用RateLimitHandler提供的专业建议
                String advice = rateLimitHandler.getErrorAdvice(apiKey, e);
                String errorMessage = "调用Claude API失败: " + e.getMessage() + "\n" + advice;

                // 对于429错误，重新抛出以触发Resilience4j重试机制
                throw new RuntimeException("429 Too Many Requests - Rate limit exceeded");
            }

            log.error("Claude 调用异常", e);

            // 根据错误类型提供更详细的错误信息
            String errorMessage = "调用Claude API失败: " + e.getMessage();
            if (e.getMessage() != null) {
                if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                    errorMessage += "\n可能的原因：\n1. API Key 无效或已过期\n2. 未开通对应的模型服务\n3. API Key 格式不正确";
                } else if (e.getMessage().contains("403") || e.getMessage().contains("Forbidden")) {
                    errorMessage += "\n可能的原因：\n1. API Key 权限不足\n2. 账号余额不足\n3. 请求频率超限";
                } else if (e.getMessage().contains("timeout") || e.getMessage().contains("connect")) {
                    errorMessage += "\n可能的原因：\n1. 网络连接问题\n2. Claude服务暂时不可用\n3. 请求超时";
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            return AiResult.builder().content(errorMessage).usage(new HashMap<>()).durationMs(duration).build();
        }
    }

    /**
     * 流式调用 Claude API
     */
    @Override
    @Bulkhead(name = "claude-stream", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "claude-stream")
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        try {
            WebClient client = webClientBuilder
                .baseUrl(cfg.getApiUrl())
                .defaultHeader("x-api-key", cfg.getApiKey())
                .defaultHeader("anthropic-version", "2023-06-01")
                .defaultHeader("content-type", "application/json")
                .defaultHeader("accept", "text/event-stream")
                .build();

            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(
                new AiInvocationRequestDTO(dto.getToolKey(), dto.getPrompt(), dto.getMetadata(), dto.getTenantId(), dto.getEmployeeId())
            );
            log.debug("流式调用 - 原始提示词: {}", originalPrompt);
            log.debug("流式调用 - 增强提示词: {}", enhancedPrompt);

            // 构建 Claude API 请求
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", defaultModel);
            requestBody.put("max_tokens", defaultMaxTokens);
            requestBody.put("temperature", defaultTemperature);
            requestBody.put("system", "You are a helpful AI assistant.");
            requestBody.put("stream", true);

            // 添加用户消息
            ArrayNode messagesArray = objectMapper.createArrayNode();
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");

            // 构建内容数组
            ArrayNode contentArray = objectMapper.createArrayNode();
            ObjectNode textContent = objectMapper.createObjectNode();
            textContent.put("type", "text");
            textContent.put("text", enhancedPrompt);
            contentArray.add(textContent);

            userMessage.set("content", contentArray);
            messagesArray.add(userMessage);
            requestBody.set("messages", messagesArray);

            // 添加其他可选参数
            if (dto.getMetadata() != null) {
                if (dto.getMetadata().containsKey("temperature")) {
                    requestBody.put("temperature", (Double) dto.getMetadata().get("temperature"));
                }
                if (dto.getMetadata().containsKey("max_tokens")) {
                    requestBody.put("max_tokens", (Integer) dto.getMetadata().get("max_tokens"));
                }
                if (dto.getMetadata().containsKey("model")) {
                    requestBody.put("model", (String) dto.getMetadata().get("model"));
                }
                if (dto.getMetadata().containsKey("system")) {
                    requestBody.put("system", (String) dto.getMetadata().get("system"));
                }
            }

            log.debug("Claude 流式请求体: {}", requestBody);
            log.debug("Claude API URL: {}, Path: {}", cfg.getApiUrl(), cfg.getPath());
            log.debug("Claude API Key (部分): {}", cfg.getApiKey().substring(0, Math.min(10, cfg.getApiKey().length())) + "...");

            // 用于累计完整响应以计算最终使用量
            AtomicReference<StringBuilder> fullResponseBuilder = new AtomicReference<>(new StringBuilder());

            // 发送流式请求
            return client
                .post()
                .uri(cfg.getPath())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestBody)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .map(chunk -> {
                    try {
                        // 处理 SSE 格式的响应
                        if (chunk.startsWith("data: ")) {
                            chunk = chunk.substring(6);
                        }

                        // 处理流结束标记
                        if ("[DONE]".equals(chunk.trim())) {
                            Map<String, Object> usage = new HashMap<>();
                            usage.put("total_tokens", fullResponseBuilder.get().length() / 4); // 粗略估计
                            return new AiStreamResponseDTO("", usage);
                        }

                        // 解析 JSON 响应
                        JsonNode response = objectMapper.readTree(chunk);

                        // 根据事件类型处理
                        if (response.has("type")) {
                            String eventType = response.get("type").asText();

                            if ("content_block_delta".equals(eventType)) {
                                // 处理增量内容
                                JsonNode delta = response.get("delta");
                                if (delta != null && delta.has("text")) {
                                    String text = delta.get("text").asText();
                                    if (text != null && !text.isEmpty()) {
                                        fullResponseBuilder.get().append(text);
                                        return new AiStreamResponseDTO(text, false);
                                    }
                                }
                            } else if ("content_block_start".equals(eventType)) {
                                // 处理内容块开始事件
                                JsonNode contentBlock = response.get("content_block");
                                if (contentBlock != null && "text".equals(contentBlock.get("type").asText())) {
                                    String text = contentBlock.get("text").asText();
                                    if (text != null && !text.isEmpty()) {
                                        fullResponseBuilder.get().append(text);
                                        return new AiStreamResponseDTO(text, false);
                                    }
                                }
                            } else if ("message_stop".equals(eventType)) {
                                // 处理消息结束事件
                                JsonNode usage = response.get("usage");
                                if (usage != null) {
                                    Map<String, Object> usageMap = new HashMap<>();
                                    usageMap.put("input_tokens", usage.get("input_tokens").asInt());
                                    usageMap.put("output_tokens", usage.get("output_tokens").asInt());
                                    usageMap.put("total_tokens", usage.get("input_tokens").asInt() + usage.get("output_tokens").asInt());
                                    return new AiStreamResponseDTO("", usageMap);
                                }
                            }
                        }

                        // 默认返回空内容
                        return new AiStreamResponseDTO("", false);
                    } catch (Exception e) {
                        log.error("解析Claude流式响应失败", e);
                        return new AiStreamResponseDTO("解析Claude流式响应失败: " + e.getMessage());
                    }
                })
                .onErrorResume(e -> {
                    log.error("Claude流式调用失败", e);
                    return Flux.just(new AiStreamResponseDTO("Claude流式调用失败: " + e.getMessage()));
                });
        } catch (Exception e) {
            log.error("准备Claude流式调用失败", e);
            return Flux.just(new AiStreamResponseDTO("准备Claude流式调用失败: " + e.getMessage()));
        }
    }

    /**
     * 从 Claude 响应消息中提取文本内容
     *
     * @param message Claude API 响应消息
     * @return 提取的文本内容
     */
    private String extractContentFromMessage(Message message) {
        if (message.content() == null || message.content().isEmpty()) {
            return "";
        }

        StringBuilder content = new StringBuilder();
        for (ContentBlock block : message.content()) {
            // if (block instanceof TextBlock) {
            //     TextBlock textBlock = (TextBlock) block;
            //     content.append(textBlock.text());
            // } 这里代码注释了因为报错 后期再拉修复

        }
        return content.toString();
    }
}
