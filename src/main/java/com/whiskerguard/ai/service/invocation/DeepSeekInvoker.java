/**
 * DeepSeek 调用实现类
 * <p>
 * 负责与 DeepSeek API 进行通信，支持 RAG 增强功能。
 */
package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.util.RateLimitHandler;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@SuppressWarnings("unchecked")
public class DeepSeekInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(DeepSeekInvoker.class);
    private final WebClient.Builder webClientBuilder;
    private final RagHelper ragHelper;
    private final RateLimitHandler rateLimitHandler;
    private final ObjectMapper objectMapper;

    public DeepSeekInvoker(
        WebClient.Builder webClientBuilder,
        RagHelper ragHelper,
        RateLimitHandler rateLimitHandler,
        ObjectMapper objectMapper
    ) {
        this.webClientBuilder = webClientBuilder;
        this.ragHelper = ragHelper;
        this.rateLimitHandler = rateLimitHandler;
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String getToolKey() {
        return "deepseek";
    }

    @Override
    @Bulkhead(name = "deepseek", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "deepseek")
    @Retry(name = "deepseek")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        String apiKey = "deepseek";

        try {
            // 检查是否需要延迟调用
            long delayMs = rateLimitHandler.shouldDelayCall(apiKey);
            if (delayMs > 0) {
                log.info("DeepSeek API调用频率控制，延迟{}ms", delayMs);
                Thread.sleep(delayMs);
            }
            WebClient client = webClientBuilder
                .baseUrl(cfg.getApiUrl())
                .defaultHeader("Authorization", "Bearer " + cfg.getApiKey())
                .build();

            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("原始提示词: {}", originalPrompt);
            log.debug("增强提示词: {}", enhancedPrompt);

            // 构建DeepSeek API请求体
            Map<String, Object> body = new HashMap<>();

            // 构建消息数组
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", enhancedPrompt);
            messages.add(userMessage);

            body.put("model", "deepseek-chat"); // 默认使用deepseek-chat模型
            body.put("messages", messages);

            // 添加其他可选参数
            if (dto.getMetadata() != null) {
                // 如果元数据中有temperature, max_tokens等参数，添加到请求中
                if (dto.getMetadata().containsKey("temperature")) {
                    body.put("temperature", dto.getMetadata().get("temperature"));
                }
                if (dto.getMetadata().containsKey("max_tokens")) {
                    body.put("max_tokens", dto.getMetadata().get("max_tokens"));
                }
            }

            log.debug("DeepSeek请求体: {}", body);

            long start = System.currentTimeMillis();
            Map<String, Object> resp = client.post().uri(cfg.getPath()).bodyValue(body).retrieve().bodyToMono(Map.class).block();
            long duration = System.currentTimeMillis() - start;

            log.debug("DeepSeek响应: {}", resp);

            // DeepSeek 响应解析
            Map<String, Object> choices = (Map<String, Object>) ((List<Object>) resp.get("choices")).get(0);
            Map<String, Object> message = (Map<String, Object>) choices.get("message");
            String content = (String) message.get("content");

            // 提取使用量信息
            Map<String, Object> usage = (Map<String, Object>) resp.get("usage");

            // 记录成功调用
            rateLimitHandler.recordSuccessfulCall(apiKey);

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            // 检查是否为速率限制错误
            if (rateLimitHandler.isRateLimitError(e)) {
                rateLimitHandler.recordRateLimitError(apiKey);
                log.warn("DeepSeek API遇到速率限制: {}", e.getMessage());

                // 使用RateLimitHandler提供的专业建议
                String advice = rateLimitHandler.getErrorAdvice(apiKey, e);
                String errorMessage = "调用DeepSeek API失败: " + e.getMessage() + "\n" + advice;

                // 对于429错误，重新抛出以触发Resilience4j重试机制
                throw new RuntimeException("429 Too Many Requests - Rate limit exceeded");
            }

            log.error("调用DeepSeek API失败", e);

            // 根据错误类型提供更详细的错误信息
            String errorMessage = "调用DeepSeek API失败: " + e.getMessage();
            if (e.getMessage() != null) {
                if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                    errorMessage += "\n可能的原因：\n1. API Key 无效或已过期\n2. 未开通对应的模型服务\n3. API Key 格式不正确";
                } else if (e.getMessage().contains("403") || e.getMessage().contains("Forbidden")) {
                    errorMessage += "\n可能的原因：\n1. API Key 权限不足\n2. 账号余额不足\n3. 请求频率超限";
                } else if (e.getMessage().contains("timeout") || e.getMessage().contains("connect")) {
                    errorMessage += "\n可能的原因：\n1. 网络连接问题\n2. DeepSeek服务暂时不可用\n3. 请求超时";
                }
            }

            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            return AiResult.builder().content(errorMessage).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    @Bulkhead(name = "deepseek-stream", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "deepseek-stream")
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        WebClient client = webClientBuilder.baseUrl(cfg.getApiUrl()).defaultHeader("Authorization", "Bearer " + cfg.getApiKey()).build();

        // 使用 RAG 增强提示词
        String originalPrompt = dto.getPrompt();
        String enhancedPrompt = ragHelper.enhancePromptWithRag(
            new AiInvocationRequestDTO(dto.getToolKey(), dto.getPrompt(), dto.getMetadata(), dto.getTenantId(), dto.getEmployeeId())
        );
        log.debug("流式调用 - 原始提示词: {}", originalPrompt);
        log.debug("流式调用 - 增强提示词: {}", enhancedPrompt);

        // 构建DeepSeek API请求体
        Map<String, Object> body = new HashMap<>();

        // 构建消息数组
        List<Map<String, String>> messages = new ArrayList<>();
        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", enhancedPrompt);
        messages.add(userMessage);

        body.put("model", "deepseek-chat"); // 默认使用deepseek-chat模型
        body.put("messages", messages);
        body.put("stream", true); // 启用流式输出

        // 添加其他可选参数
        if (dto.getMetadata() != null) {
            // 如果元数据中有temperature, max_tokens等参数，添加到请求中
            if (dto.getMetadata().containsKey("temperature")) {
                body.put("temperature", dto.getMetadata().get("temperature"));
            }
            if (dto.getMetadata().containsKey("max_tokens")) {
                body.put("max_tokens", dto.getMetadata().get("max_tokens"));
            }
        }

        log.debug("DeepSeek流式请求体: {}", body);

        try {
            // 非流式实现，但返回Flux以符合接口要求
            return client
                .post()
                .uri(cfg.getPath())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body)
                .retrieve()
                .bodyToMono(Map.class)
                .flatMapMany(resp -> {
                    log.debug("DeepSeek响应: {}", resp);

                    // DeepSeek 响应解析
                    Map<String, Object> choices = (Map<String, Object>) ((List<Object>) resp.get("choices")).get(0);
                    Map<String, Object> message = (Map<String, Object>) choices.get("message");
                    String content = (String) message.get("content");

                    // 提取使用量信息
                    Map<String, Object> usage = (Map<String, Object>) resp.get("usage");

                    return Flux.just(new AiStreamResponseDTO(content, usage));
                })
                .onErrorResume(e -> {
                    log.error("DeepSeek 流式调用失败", e);
                    return Flux.just(new AiStreamResponseDTO("DeepSeek API 调用出错: " + e.getMessage(), true));
                })
                .timeout(Duration.ofSeconds(30))
                .onErrorResume(TimeoutException.class, e -> {
                    log.error("DeepSeek 流式请求超时", e);
                    return Flux.just(new AiStreamResponseDTO("请求超时，请稍后再试", true));
                });
        } catch (Exception e) {
            log.error("DeepSeek 流式调用失败", e);
            return Flux.just(new AiStreamResponseDTO("DeepSeek API 调用出错: " + e.getMessage(), true));
        }
    }
}
