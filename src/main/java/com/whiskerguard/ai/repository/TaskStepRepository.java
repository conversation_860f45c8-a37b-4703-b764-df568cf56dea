package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the TaskStep entity.
 */
@Repository
public interface TaskStepRepository extends JpaRepository<TaskStep, Long> {
    /**
     * 根据任务ID查找所有未删除的步骤
     * Find all non-deleted steps by agent task ID
     */
    @Query("SELECT ts FROM TaskStep ts WHERE ts.agentTask.id = :agentTaskId AND ts.isDeleted = false")
    List<TaskStep> findByAgentTaskIdAndIsDeletedFalse(@Param("agentTaskId") Long agentTaskId);

    /**
     * 根据任务ID查找所有未删除的步骤，按步骤顺序排序
     * Find all non-deleted steps by agent task ID, ordered by step order
     */
    @Query("SELECT ts FROM TaskStep ts WHERE ts.agentTask.id = :agentTaskId AND ts.isDeleted = false ORDER BY ts.stepOrder")
    List<TaskStep> findByAgentTaskIdAndIsDeletedFalseOrderByStepOrder(@Param("agentTaskId") Long agentTaskId);

    /**
     * 根据任务ID和状态查找未删除的步骤
     * Find non-deleted steps by agent task ID and status
     */
    @Query("SELECT ts FROM TaskStep ts WHERE ts.agentTask.id = :agentTaskId AND ts.status = :status AND ts.isDeleted = false")
    List<TaskStep> findByAgentTaskIdAndStatusAndIsDeletedFalse(
        @Param("agentTaskId") Long agentTaskId,
        @Param("status") TaskStepStatus status
    );

    /**
     * 根据任务ID和步骤类型查找未删除的步骤
     * Find non-deleted steps by agent task ID and step type
     */
    @Query("SELECT ts FROM TaskStep ts WHERE ts.agentTask.id = :agentTaskId AND ts.stepType = :stepType AND ts.isDeleted = false")
    List<TaskStep> findByAgentTaskIdAndStepTypeAndIsDeletedFalse(
        @Param("agentTaskId") Long agentTaskId,
        @Param("stepType") String stepType
    );

    default Optional<TaskStep> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<TaskStep> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<TaskStep> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    @Query(
        value = "select taskStep from TaskStep taskStep left join fetch taskStep.agentTask",
        countQuery = "select count(taskStep) from TaskStep taskStep"
    )
    Page<TaskStep> findAllWithToOneRelationships(Pageable pageable);

    @Query("select taskStep from TaskStep taskStep left join fetch taskStep.agentTask")
    List<TaskStep> findAllWithToOneRelationships();

    @Query("select taskStep from TaskStep taskStep left join fetch taskStep.agentTask where taskStep.id =:id")
    Optional<TaskStep> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * 根据任务ID查找所有步骤，按步骤顺序排序
     */
    List<TaskStep> findByAgentTaskIdOrderByStepOrder(Long taskId);
}
