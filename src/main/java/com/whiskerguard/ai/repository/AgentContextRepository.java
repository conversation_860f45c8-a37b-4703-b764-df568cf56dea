package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AgentContext;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the AgentContext entity.
 */
@Repository
public interface AgentContextRepository extends JpaRepository<AgentContext, Long> {
    /**
     * 根据任务ID查找上下文
     * Find context by agent task ID
     *
     * @param agentTaskId 任务ID / Agent task ID
     * @return 上下文实体 / Context entity
     */
    Optional<AgentContext> findByAgentTaskId(Long agentTaskId);

    /**
     * 查找过期的上下文（软删除）
     * Find expired contexts (soft delete)
     *
     * @param cutoffTime 截止时间 / Cutoff time
     * @return 过期的上下文列表 / List of expired contexts
     */
    @Query("SELECT ac FROM AgentContext ac WHERE ac.createdAt < :cutoffTime AND ac.isDeleted = false")
    List<AgentContext> findExpiredContexts(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 根据任务类型查找过期的上下文
     * Find expired contexts by task type
     *
     * @param cutoffTime 截止时间 / Cutoff time
     * @param contextType 上下文类型 / Context type
     * @return 过期的上下文列表 / List of expired contexts
     */
    @Query("SELECT ac FROM AgentContext ac WHERE ac.createdAt < :cutoffTime AND ac.contextType = :contextType AND ac.isDeleted = false")
    List<AgentContext> findExpiredContextsByTaskType(@Param("cutoffTime") Instant cutoffTime, @Param("contextType") String contextType);

    /**
     * 根据任务ID列表批量查找上下文
     * Find contexts by agent task IDs in batch
     *
     * @param agentTaskIds 任务ID列表 / List of agent task IDs
     * @return 上下文列表 / List of contexts
     */
    @Query("SELECT ac FROM AgentContext ac WHERE ac.agentTaskId IN :agentTaskIds AND ac.isDeleted = false")
    List<AgentContext> findByAgentTaskIdIn(@Param("agentTaskIds") List<Long> agentTaskIds);

    default Optional<AgentContext> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<AgentContext> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<AgentContext> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    @Query(
        value = "select agentContext from AgentContext agentContext left join fetch agentContext.agentTask",
        countQuery = "select count(agentContext) from AgentContext agentContext"
    )
    Page<AgentContext> findAllWithToOneRelationships(Pageable pageable);

    @Query("select agentContext from AgentContext agentContext left join fetch agentContext.agentTask")
    List<AgentContext> findAllWithToOneRelationships();

    @Query("select agentContext from AgentContext agentContext left join fetch agentContext.agentTask where agentContext.id =:id")
    Optional<AgentContext> findOneWithToOneRelationships(@Param("id") Long id);
}
