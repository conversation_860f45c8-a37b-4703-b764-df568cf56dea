package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AgentConfig;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the AgentConfig entity.
 * Agent配置仓库接口
 */
@SuppressWarnings("unused")
@Repository
public interface AgentConfigRepository extends JpaRepository<AgentConfig, Long> {
    /**
     * 根据配置键、租户ID和任务类型查找配置
     * Find configuration by config key, tenant ID and task type
     */
    @Query(
        "SELECT ac FROM AgentConfig ac WHERE ac.configKey = :configKey " +
        "AND (ac.tenantId = :tenantId OR ac.tenantId IS NULL) " +
        "AND (ac.taskType = :taskType OR ac.taskType IS NULL) " +
        "AND ac.enabled = true AND ac.isDeleted = false " +
        "ORDER BY ac.priority DESC, ac.tenantId DESC NULLS LAST, ac.taskType DESC NULLS LAST"
    )
    List<AgentConfig> findByConfigKeyAndTenantAndTaskType(
        @Param("configKey") String configKey,
        @Param("tenantId") Long tenantId,
        @Param("taskType") AgentTaskType taskType
    );

    /**
     * 根据配置键和租户ID查找配置（不考虑任务类型）
     * Find configuration by config key and tenant ID (ignoring task type)
     */
    @Query(
        "SELECT ac FROM AgentConfig ac WHERE ac.configKey = :configKey " +
        "AND (ac.tenantId = :tenantId OR ac.tenantId IS NULL) " +
        "AND ac.enabled = true AND ac.isDeleted = false " +
        "ORDER BY ac.priority DESC, ac.tenantId DESC NULLS LAST"
    )
    List<AgentConfig> findByConfigKeyAndTenant(@Param("configKey") String configKey, @Param("tenantId") Long tenantId);

    /**
     * 根据配置键查找全局配置
     * Find global configuration by config key
     */
    @Query(
        "SELECT ac FROM AgentConfig ac WHERE ac.configKey = :configKey " +
        "AND ac.tenantId IS NULL AND ac.taskType IS NULL " +
        "AND ac.enabled = true AND ac.isDeleted = false " +
        "ORDER BY ac.priority DESC"
    )
    List<AgentConfig> findGlobalConfigByKey(@Param("configKey") String configKey);

    /**
     * 根据租户ID查找所有配置
     * Find all configurations by tenant ID
     */
    @Query(
        "SELECT ac FROM AgentConfig ac WHERE ac.tenantId = :tenantId " +
        "AND ac.enabled = true AND ac.isDeleted = false " +
        "ORDER BY ac.configGroup, ac.configKey"
    )
    List<AgentConfig> findAllByTenant(@Param("tenantId") Long tenantId);

    /**
     * 根据配置组查找配置
     * Find configurations by config group
     */
    @Query(
        "SELECT ac FROM AgentConfig ac WHERE ac.configGroup = :configGroup " +
        "AND (ac.tenantId = :tenantId OR ac.tenantId IS NULL) " +
        "AND ac.enabled = true AND ac.isDeleted = false " +
        "ORDER BY ac.priority DESC, ac.configKey"
    )
    List<AgentConfig> findByConfigGroupAndTenant(@Param("configGroup") String configGroup, @Param("tenantId") Long tenantId);

    /**
     * 根据配置类型查找配置
     * Find configurations by config type
     */
    @Query(
        "SELECT ac FROM AgentConfig ac WHERE ac.configType = :configType " +
        "AND (ac.tenantId = :tenantId OR ac.tenantId IS NULL) " +
        "AND ac.enabled = true AND ac.isDeleted = false " +
        "ORDER BY ac.priority DESC, ac.configKey"
    )
    List<AgentConfig> findByConfigTypeAndTenant(@Param("configType") String configType, @Param("tenantId") Long tenantId);

    /**
     * 检查配置是否存在
     * Check if configuration exists
     */
    @Query(
        "SELECT COUNT(ac) > 0 FROM AgentConfig ac WHERE ac.configKey = :configKey " +
        "AND (ac.tenantId = :tenantId OR ac.tenantId IS NULL) " +
        "AND (ac.taskType = :taskType OR ac.taskType IS NULL) " +
        "AND ac.enabled = true AND ac.isDeleted = false"
    )
    boolean existsByConfigKeyAndTenantAndTaskType(
        @Param("configKey") String configKey,
        @Param("tenantId") Long tenantId,
        @Param("taskType") AgentTaskType taskType
    );

    /**
     * 根据配置键、租户ID、任务类型查找精确匹配的配置
     * Find exact configuration by config key, tenant ID and task type
     */
    Optional<AgentConfig> findByConfigKeyAndTenantIdAndTaskTypeAndIsDeletedFalse(String configKey, Long tenantId, AgentTaskType taskType);

    /**
     * 根据配置键和租户ID查找配置（任务类型为null）
     * Find configuration by config key and tenant ID (task type is null)
     */
    Optional<AgentConfig> findByConfigKeyAndTenantIdAndTaskTypeIsNullAndIsDeletedFalse(String configKey, Long tenantId);

    /**
     * 根据配置键查找全局配置（租户ID和任务类型都为null）
     * Find global configuration (both tenant ID and task type are null)
     */
    Optional<AgentConfig> findByConfigKeyAndTenantIdIsNullAndTaskTypeIsNullAndIsDeletedFalse(String configKey);
}
