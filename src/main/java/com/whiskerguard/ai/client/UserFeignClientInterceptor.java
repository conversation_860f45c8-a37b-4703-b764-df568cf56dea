package com.whiskerguard.ai.client;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Feign 客户端认证拦截器
 * <p>
 * 自动将当前请求的认证信息传递给下游微服务调用
 * 支持优雅降级，当没有认证信息时不会阻断调用
 */
@Component
public class UserFeignClientInterceptor implements RequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(UserFeignClientInterceptor.class);

    @Override
    public void apply(RequestTemplate template) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String token = request.getHeader("Authorization");
                template.header("Authorization", token);
                template.header("X-TENANT-ID", request.getHeader("X-TENANT-ID"));
                template.header("X-USER-ID", request.getHeader("X-USER-ID"));
                template.header("X-USER-NAME", request.getHeader("X-USER-NAME"));
                template.header("X-SOURCE", request.getHeader("X-SOURCE"));
                template.header("X-VERSION", request.getHeader("X-VERSION"));
                template.header("X-Forwarded-For", request.getHeader("X-Forwarded-For"));
            }
        } catch (Exception e) {
            log.warn("处理 Feign 认证拦截时发生错误，请求将继续但不包含认证信息: {}", e.getMessage());
        }
    }
}
