#!/bin/bash

# =============================================================================
# TaskStep集成测试脚本
# 验证ComplianceAgentController的createTask端点是否正确创建TaskStep
# =============================================================================

echo "=== TaskStep集成测试开始 ==="

# 1. 测试合同审查任务创建
echo "1. 测试合同审查任务创建..."
curl -X POST http://localhost:8080/api/compliance-agent/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": 1,
    "taskType": "CONTRACT_REVIEW",
    "title": "合同审查TaskStep集成测试",
    "description": "测试TaskStep是否正确创建",
    "priority": "NORMAL",
    "requestData": "{\"contractId\":\"TEST_001\",\"contractType\":\"采购合同\",\"tenantId\":1,\"employeeId\":1001}"
  }' | jq '.'

echo ""
echo "等待3秒让异步任务执行..."
sleep 3

# 2. 测试外规内化任务创建
echo "2. 测试外规内化任务创建..."
curl -X POST http://localhost:8080/api/compliance-agent/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": 1,
    "taskType": "REGULATION_INTERNALIZATION", 
    "title": "外规内化TaskStep集成测试",
    "description": "测试外规内化TaskStep创建",
    "priority": "HIGH",
    "requestData": "{\"regulationId\":\"REG_001\",\"companyId\":1,\"industryType\":\"电力\",\"tenantId\":1,\"employeeId\":1001}"
  }' | jq '.'

echo ""
echo "等待3秒让异步任务执行..."
sleep 3

# 3. 测试制度审查任务创建
echo "3. 测试制度审查任务创建..."
curl -X POST http://localhost:8080/api/compliance-agent/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": 1,
    "taskType": "POLICY_REVIEW",
    "title": "制度审查TaskStep集成测试", 
    "description": "测试制度审查TaskStep创建",
    "priority": "NORMAL",
    "requestData": "{\"policyId\":\"POL_001\",\"policyType\":\"安全管理\",\"tenantId\":1,\"employeeId\":1001}"
  }' | jq '.'

echo ""
echo "=== TaskStep集成测试完成 ==="
echo ""
echo "请检查应用程序日志，确认以下内容："
echo "1. 'createTaskSteps' 方法被调用"
echo "2. 'createTaskContext' 方法被调用"
echo "3. TaskStep实体被正确创建和保存"
echo "4. AgentContext实体被正确创建和保存"
echo ""
echo "可以通过以下SQL查询验证数据库中的记录："
echo "SELECT * FROM agent_task ORDER BY created_at DESC LIMIT 5;"
echo "SELECT * FROM task_step ORDER BY created_at DESC LIMIT 10;"
echo "SELECT * FROM agent_context ORDER BY created_at DESC LIMIT 5;"
